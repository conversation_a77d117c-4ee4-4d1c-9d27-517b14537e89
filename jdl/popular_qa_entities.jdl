/**
 * AI热门问答功能实体定义
 * 
 * 功能说明：
 * 1. 前端显示热门问题列表，用户点击后显示预设答案
 * 2. 支持分类浏览、热门推荐、历史记录
 * 3. 企业管理员后台CRUD维护
 * 4. 多租户数据隔离
 * 5. 与现有聊天系统集成
 */

/**
 * 热门问答分类枚举
 */
enum PopularQaCategory {
    REGULATORY_COMPLIANCE("外规内化"),
    POLICY_REVIEW("制度审查"), 
    CONTRACT_REVIEW("合同审查"),
    GENERAL_CONSULTATION("通用咨询"),
    LEGAL_ADVICE("法律建议"),
    COMMON_QUESTIONS("常见问题")
}

/**
 * AI热门问答实体
 * 存储企业管理员维护的常见问题和答案
 */
entity PopularQa {
    /** 主键 ID */
    id Long,
    /** 租户 ID */
    tenantId Long required,
    /** 问题标题 */
    questionTitle String required maxlength(200),
    /** 问题内容 */
    questionContent String required,
    /** 答案内容 */
    answerContent String required,
    /** 问题分类 */
    category PopularQaCategory required,
    /** 排序权重（数值越大越靠前） */
    sortOrder Integer,
    /** 点击次数 */
    clickCount Long,
    /** 是否启用 */
    isEnabled Boolean required,
    /** 是否为热门推荐 */
    isTrending Boolean,
    /** 标签（多个标签用逗号分隔） */
    tags String maxlength(500),
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String,
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String,
    /** 更新时间 */
    updatedAt Instant required,
    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * 热门问答点击记录实体
 * 用于统计和分析用户使用情况
 */
entity PopularQaClickLog {
    /** 主键 ID */
    id Long,
    /** 租户 ID */
    tenantId Long required,
    /** 员工 ID */
    employeeId Long required,
    /** 问答 ID */
    qaId Long required,
    /** 点击时间 */
    clickTime Instant required,
    /** 用户IP */
    userIp String maxlength(45),
    /** 用户代理 */
    userAgent String maxlength(500),
    /** 会话ID（关联到后续聊天） */
    conversationId String maxlength(64)
}

// 关系定义
relationship ManyToOne {
    PopularQaClickLog{qa} to PopularQa
}

// 分页配置
paginate PopularQa, PopularQaClickLog with pagination

// DTO配置
dto * with mapstruct

// 服务配置
service all with serviceImpl

// 搜索配置
search PopularQa with no
