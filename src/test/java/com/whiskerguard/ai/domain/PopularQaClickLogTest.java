package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.PopularQaClickLogTestSamples.*;
import static com.whiskerguard.ai.domain.PopularQaTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class PopularQaClickLogTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(PopularQaClickLog.class);
        PopularQaClickLog popularQaClickLog1 = getPopularQaClickLogSample1();
        PopularQaClickLog popularQaClickLog2 = new PopularQaClickLog();
        assertThat(popularQaClickLog1).isNotEqualTo(popularQaClickLog2);

        popularQaClickLog2.setId(popularQaClickLog1.getId());
        assertThat(popularQaClickLog1).isEqualTo(popularQaClickLog2);

        popularQaClickLog2 = getPopularQaClickLogSample2();
        assertThat(popularQaClickLog1).isNotEqualTo(popularQaClickLog2);
    }

    @Test
    void qaTest() {
        PopularQaClickLog popularQaClickLog = getPopularQaClickLogRandomSampleGenerator();
        PopularQa popularQaBack = getPopularQaRandomSampleGenerator();

        popularQaClickLog.setQa(popularQaBack);
        assertThat(popularQaClickLog.getQa()).isEqualTo(popularQaBack);

        popularQaClickLog.qa(null);
        assertThat(popularQaClickLog.getQa()).isNull();
    }
}
