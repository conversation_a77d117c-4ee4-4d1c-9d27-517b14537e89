package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.PopularQaClickLogAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.PopularQaClickLog;
import com.whiskerguard.ai.repository.PopularQaClickLogRepository;
import com.whiskerguard.ai.service.dto.PopularQaClickLogDTO;
import com.whiskerguard.ai.service.mapper.PopularQaClickLogMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link PopularQaClickLogResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class PopularQaClickLogResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Long DEFAULT_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_EMPLOYEE_ID = 2L;

    private static final Long DEFAULT_QA_ID = 1L;
    private static final Long UPDATED_QA_ID = 2L;

    private static final Instant DEFAULT_CLICK_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CLICK_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_USER_IP = "AAAAAAAAAA";
    private static final String UPDATED_USER_IP = "BBBBBBBBBB";

    private static final String DEFAULT_USER_AGENT = "AAAAAAAAAA";
    private static final String UPDATED_USER_AGENT = "BBBBBBBBBB";

    private static final String DEFAULT_CONVERSATION_ID = "AAAAAAAAAA";
    private static final String UPDATED_CONVERSATION_ID = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/popular-qa-click-logs";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private PopularQaClickLogRepository popularQaClickLogRepository;

    @Autowired
    private PopularQaClickLogMapper popularQaClickLogMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restPopularQaClickLogMockMvc;

    private PopularQaClickLog popularQaClickLog;

    private PopularQaClickLog insertedPopularQaClickLog;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static PopularQaClickLog createEntity() {
        return new PopularQaClickLog()
            .tenantId(DEFAULT_TENANT_ID)
            .employeeId(DEFAULT_EMPLOYEE_ID)
            .qaId(DEFAULT_QA_ID)
            .clickTime(DEFAULT_CLICK_TIME)
            .userIp(DEFAULT_USER_IP)
            .userAgent(DEFAULT_USER_AGENT)
            .conversationId(DEFAULT_CONVERSATION_ID);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static PopularQaClickLog createUpdatedEntity() {
        return new PopularQaClickLog()
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .qaId(UPDATED_QA_ID)
            .clickTime(UPDATED_CLICK_TIME)
            .userIp(UPDATED_USER_IP)
            .userAgent(UPDATED_USER_AGENT)
            .conversationId(UPDATED_CONVERSATION_ID);
    }

    @BeforeEach
    void initTest() {
        popularQaClickLog = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedPopularQaClickLog != null) {
            popularQaClickLogRepository.delete(insertedPopularQaClickLog);
            insertedPopularQaClickLog = null;
        }
    }

    @Test
    @Transactional
    void createPopularQaClickLog() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the PopularQaClickLog
        PopularQaClickLogDTO popularQaClickLogDTO = popularQaClickLogMapper.toDto(popularQaClickLog);
        var returnedPopularQaClickLogDTO = om.readValue(
            restPopularQaClickLogMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaClickLogDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            PopularQaClickLogDTO.class
        );

        // Validate the PopularQaClickLog in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedPopularQaClickLog = popularQaClickLogMapper.toEntity(returnedPopularQaClickLogDTO);
        assertPopularQaClickLogUpdatableFieldsEquals(returnedPopularQaClickLog, getPersistedPopularQaClickLog(returnedPopularQaClickLog));

        insertedPopularQaClickLog = returnedPopularQaClickLog;
    }

    @Test
    @Transactional
    void createPopularQaClickLogWithExistingId() throws Exception {
        // Create the PopularQaClickLog with an existing ID
        popularQaClickLog.setId(1L);
        PopularQaClickLogDTO popularQaClickLogDTO = popularQaClickLogMapper.toDto(popularQaClickLog);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restPopularQaClickLogMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaClickLogDTO)))
            .andExpect(status().isBadRequest());

        // Validate the PopularQaClickLog in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        popularQaClickLog.setTenantId(null);

        // Create the PopularQaClickLog, which fails.
        PopularQaClickLogDTO popularQaClickLogDTO = popularQaClickLogMapper.toDto(popularQaClickLog);

        restPopularQaClickLogMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaClickLogDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkEmployeeIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        popularQaClickLog.setEmployeeId(null);

        // Create the PopularQaClickLog, which fails.
        PopularQaClickLogDTO popularQaClickLogDTO = popularQaClickLogMapper.toDto(popularQaClickLog);

        restPopularQaClickLogMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaClickLogDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkQaIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        popularQaClickLog.setQaId(null);

        // Create the PopularQaClickLog, which fails.
        PopularQaClickLogDTO popularQaClickLogDTO = popularQaClickLogMapper.toDto(popularQaClickLog);

        restPopularQaClickLogMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaClickLogDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkClickTimeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        popularQaClickLog.setClickTime(null);

        // Create the PopularQaClickLog, which fails.
        PopularQaClickLogDTO popularQaClickLogDTO = popularQaClickLogMapper.toDto(popularQaClickLog);

        restPopularQaClickLogMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaClickLogDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllPopularQaClickLogs() throws Exception {
        // Initialize the database
        insertedPopularQaClickLog = popularQaClickLogRepository.saveAndFlush(popularQaClickLog);

        // Get all the popularQaClickLogList
        restPopularQaClickLogMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(popularQaClickLog.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].employeeId").value(hasItem(DEFAULT_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].qaId").value(hasItem(DEFAULT_QA_ID.intValue())))
            .andExpect(jsonPath("$.[*].clickTime").value(hasItem(DEFAULT_CLICK_TIME.toString())))
            .andExpect(jsonPath("$.[*].userIp").value(hasItem(DEFAULT_USER_IP)))
            .andExpect(jsonPath("$.[*].userAgent").value(hasItem(DEFAULT_USER_AGENT)))
            .andExpect(jsonPath("$.[*].conversationId").value(hasItem(DEFAULT_CONVERSATION_ID)));
    }

    @Test
    @Transactional
    void getPopularQaClickLog() throws Exception {
        // Initialize the database
        insertedPopularQaClickLog = popularQaClickLogRepository.saveAndFlush(popularQaClickLog);

        // Get the popularQaClickLog
        restPopularQaClickLogMockMvc
            .perform(get(ENTITY_API_URL_ID, popularQaClickLog.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(popularQaClickLog.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.employeeId").value(DEFAULT_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.qaId").value(DEFAULT_QA_ID.intValue()))
            .andExpect(jsonPath("$.clickTime").value(DEFAULT_CLICK_TIME.toString()))
            .andExpect(jsonPath("$.userIp").value(DEFAULT_USER_IP))
            .andExpect(jsonPath("$.userAgent").value(DEFAULT_USER_AGENT))
            .andExpect(jsonPath("$.conversationId").value(DEFAULT_CONVERSATION_ID));
    }

    @Test
    @Transactional
    void getNonExistingPopularQaClickLog() throws Exception {
        // Get the popularQaClickLog
        restPopularQaClickLogMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingPopularQaClickLog() throws Exception {
        // Initialize the database
        insertedPopularQaClickLog = popularQaClickLogRepository.saveAndFlush(popularQaClickLog);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the popularQaClickLog
        PopularQaClickLog updatedPopularQaClickLog = popularQaClickLogRepository.findById(popularQaClickLog.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedPopularQaClickLog are not directly saved in db
        em.detach(updatedPopularQaClickLog);
        updatedPopularQaClickLog
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .qaId(UPDATED_QA_ID)
            .clickTime(UPDATED_CLICK_TIME)
            .userIp(UPDATED_USER_IP)
            .userAgent(UPDATED_USER_AGENT)
            .conversationId(UPDATED_CONVERSATION_ID);
        PopularQaClickLogDTO popularQaClickLogDTO = popularQaClickLogMapper.toDto(updatedPopularQaClickLog);

        restPopularQaClickLogMockMvc
            .perform(
                put(ENTITY_API_URL_ID, popularQaClickLogDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(popularQaClickLogDTO))
            )
            .andExpect(status().isOk());

        // Validate the PopularQaClickLog in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedPopularQaClickLogToMatchAllProperties(updatedPopularQaClickLog);
    }

    @Test
    @Transactional
    void putNonExistingPopularQaClickLog() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        popularQaClickLog.setId(longCount.incrementAndGet());

        // Create the PopularQaClickLog
        PopularQaClickLogDTO popularQaClickLogDTO = popularQaClickLogMapper.toDto(popularQaClickLog);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restPopularQaClickLogMockMvc
            .perform(
                put(ENTITY_API_URL_ID, popularQaClickLogDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(popularQaClickLogDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PopularQaClickLog in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchPopularQaClickLog() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        popularQaClickLog.setId(longCount.incrementAndGet());

        // Create the PopularQaClickLog
        PopularQaClickLogDTO popularQaClickLogDTO = popularQaClickLogMapper.toDto(popularQaClickLog);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPopularQaClickLogMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(popularQaClickLogDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PopularQaClickLog in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamPopularQaClickLog() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        popularQaClickLog.setId(longCount.incrementAndGet());

        // Create the PopularQaClickLog
        PopularQaClickLogDTO popularQaClickLogDTO = popularQaClickLogMapper.toDto(popularQaClickLog);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPopularQaClickLogMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaClickLogDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the PopularQaClickLog in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdatePopularQaClickLogWithPatch() throws Exception {
        // Initialize the database
        insertedPopularQaClickLog = popularQaClickLogRepository.saveAndFlush(popularQaClickLog);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the popularQaClickLog using partial update
        PopularQaClickLog partialUpdatedPopularQaClickLog = new PopularQaClickLog();
        partialUpdatedPopularQaClickLog.setId(popularQaClickLog.getId());

        partialUpdatedPopularQaClickLog
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .clickTime(UPDATED_CLICK_TIME)
            .conversationId(UPDATED_CONVERSATION_ID);

        restPopularQaClickLogMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedPopularQaClickLog.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedPopularQaClickLog))
            )
            .andExpect(status().isOk());

        // Validate the PopularQaClickLog in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPopularQaClickLogUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedPopularQaClickLog, popularQaClickLog),
            getPersistedPopularQaClickLog(popularQaClickLog)
        );
    }

    @Test
    @Transactional
    void fullUpdatePopularQaClickLogWithPatch() throws Exception {
        // Initialize the database
        insertedPopularQaClickLog = popularQaClickLogRepository.saveAndFlush(popularQaClickLog);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the popularQaClickLog using partial update
        PopularQaClickLog partialUpdatedPopularQaClickLog = new PopularQaClickLog();
        partialUpdatedPopularQaClickLog.setId(popularQaClickLog.getId());

        partialUpdatedPopularQaClickLog
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .qaId(UPDATED_QA_ID)
            .clickTime(UPDATED_CLICK_TIME)
            .userIp(UPDATED_USER_IP)
            .userAgent(UPDATED_USER_AGENT)
            .conversationId(UPDATED_CONVERSATION_ID);

        restPopularQaClickLogMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedPopularQaClickLog.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedPopularQaClickLog))
            )
            .andExpect(status().isOk());

        // Validate the PopularQaClickLog in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPopularQaClickLogUpdatableFieldsEquals(
            partialUpdatedPopularQaClickLog,
            getPersistedPopularQaClickLog(partialUpdatedPopularQaClickLog)
        );
    }

    @Test
    @Transactional
    void patchNonExistingPopularQaClickLog() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        popularQaClickLog.setId(longCount.incrementAndGet());

        // Create the PopularQaClickLog
        PopularQaClickLogDTO popularQaClickLogDTO = popularQaClickLogMapper.toDto(popularQaClickLog);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restPopularQaClickLogMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, popularQaClickLogDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(popularQaClickLogDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PopularQaClickLog in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchPopularQaClickLog() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        popularQaClickLog.setId(longCount.incrementAndGet());

        // Create the PopularQaClickLog
        PopularQaClickLogDTO popularQaClickLogDTO = popularQaClickLogMapper.toDto(popularQaClickLog);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPopularQaClickLogMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(popularQaClickLogDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PopularQaClickLog in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamPopularQaClickLog() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        popularQaClickLog.setId(longCount.incrementAndGet());

        // Create the PopularQaClickLog
        PopularQaClickLogDTO popularQaClickLogDTO = popularQaClickLogMapper.toDto(popularQaClickLog);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPopularQaClickLogMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(popularQaClickLogDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the PopularQaClickLog in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deletePopularQaClickLog() throws Exception {
        // Initialize the database
        insertedPopularQaClickLog = popularQaClickLogRepository.saveAndFlush(popularQaClickLog);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the popularQaClickLog
        restPopularQaClickLogMockMvc
            .perform(delete(ENTITY_API_URL_ID, popularQaClickLog.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return popularQaClickLogRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected PopularQaClickLog getPersistedPopularQaClickLog(PopularQaClickLog popularQaClickLog) {
        return popularQaClickLogRepository.findById(popularQaClickLog.getId()).orElseThrow();
    }

    protected void assertPersistedPopularQaClickLogToMatchAllProperties(PopularQaClickLog expectedPopularQaClickLog) {
        assertPopularQaClickLogAllPropertiesEquals(expectedPopularQaClickLog, getPersistedPopularQaClickLog(expectedPopularQaClickLog));
    }

    protected void assertPersistedPopularQaClickLogToMatchUpdatableProperties(PopularQaClickLog expectedPopularQaClickLog) {
        assertPopularQaClickLogAllUpdatablePropertiesEquals(
            expectedPopularQaClickLog,
            getPersistedPopularQaClickLog(expectedPopularQaClickLog)
        );
    }
}
