package com.whiskerguard.ai.service;

import com.whiskerguard.ai.domain.enumeration.PopularQaCategory;
import com.whiskerguard.ai.service.dto.PopularQaDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.ai.domain.PopularQa}.
 */
public interface PopularQaService {
    /**
     * Save a popularQa.
     *
     * @param popularQaDTO the entity to save.
     * @return the persisted entity.
     */
    PopularQaDTO save(PopularQaDTO popularQaDTO);

    /**
     * Updates a popularQa.
     *
     * @param popularQaDTO the entity to update.
     * @return the persisted entity.
     */
    PopularQaDTO update(PopularQaDTO popularQaDTO);

    /**
     * Partially updates a popularQa.
     *
     * @param popularQaDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<PopularQaDTO> partialUpdate(PopularQaDTO popularQaDTO);

    /**
     * Get all the popularQas.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<PopularQaDTO> findAll(Pageable pageable);

    /**
     * Get the "id" popularQa.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<PopularQaDTO> findOne(Long id);

    /**
     * Delete the "id" popularQa.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);

    /**
     * 根据租户ID获取常见问题列表
     * <p>
     * 获取指定租户的启用状态的常见问题，按排序权重降序和创建时间降序排列
     *
     * @param tenantId 租户ID
     * @param pageable 分页参数
     * @return 常见问题分页列表
     */
    Page<PopularQaDTO> findCommonQuestionsByTenant(Long tenantId, Pageable pageable);

    /**
     * 根据租户ID和分类获取问答列表
     * <p>
     * 获取指定租户和分类的启用状态的问答，按排序权重降序和创建时间降序排列
     *
     * @param tenantId 租户ID
     * @param category 问答分类
     * @param pageable 分页参数
     * @return 问答分页列表
     */
    Page<PopularQaDTO> findByTenantAndCategory(Long tenantId, PopularQaCategory category, Pageable pageable);

    /**
     * 根据租户ID获取热门推荐问答
     * <p>
     * 获取指定租户的热门推荐问答，按点击量降序排列
     *
     * @param tenantId 租户ID
     * @param pageable 分页参数
     * @return 热门推荐分页列表
     */
    Page<PopularQaDTO> findTrendingByTenant(Long tenantId, Pageable pageable);

    /**
     * 增加问答点击次数
     * <p>
     * 原子性地增加指定问答的点击次数
     *
     * @param id 问答ID
     */
    void incrementClickCount(Long id);
}
