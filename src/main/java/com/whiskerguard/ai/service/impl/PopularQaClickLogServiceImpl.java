package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.PopularQaClickLog;
import com.whiskerguard.ai.repository.PopularQaClickLogRepository;
import com.whiskerguard.ai.service.PopularQaClickLogService;
import com.whiskerguard.ai.service.dto.PopularQaClickLogDTO;
import com.whiskerguard.ai.service.mapper.PopularQaClickLogMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.PopularQaClickLog}.
 */
@Service
@Transactional
public class PopularQaClickLogServiceImpl implements PopularQaClickLogService {

    private static final Logger LOG = LoggerFactory.getLogger(PopularQaClickLogServiceImpl.class);

    private final PopularQaClickLogRepository popularQaClickLogRepository;

    private final PopularQaClickLogMapper popularQaClickLogMapper;

    public PopularQaClickLogServiceImpl(
        PopularQaClickLogRepository popularQaClickLogRepository,
        PopularQaClickLogMapper popularQaClickLogMapper
    ) {
        this.popularQaClickLogRepository = popularQaClickLogRepository;
        this.popularQaClickLogMapper = popularQaClickLogMapper;
    }

    @Override
    public PopularQaClickLogDTO save(PopularQaClickLogDTO popularQaClickLogDTO) {
        LOG.debug("Request to save PopularQaClickLog : {}", popularQaClickLogDTO);
        PopularQaClickLog popularQaClickLog = popularQaClickLogMapper.toEntity(popularQaClickLogDTO);
        popularQaClickLog = popularQaClickLogRepository.save(popularQaClickLog);
        return popularQaClickLogMapper.toDto(popularQaClickLog);
    }

    @Override
    public PopularQaClickLogDTO update(PopularQaClickLogDTO popularQaClickLogDTO) {
        LOG.debug("Request to update PopularQaClickLog : {}", popularQaClickLogDTO);
        PopularQaClickLog popularQaClickLog = popularQaClickLogMapper.toEntity(popularQaClickLogDTO);
        popularQaClickLog = popularQaClickLogRepository.save(popularQaClickLog);
        return popularQaClickLogMapper.toDto(popularQaClickLog);
    }

    @Override
    public Optional<PopularQaClickLogDTO> partialUpdate(PopularQaClickLogDTO popularQaClickLogDTO) {
        LOG.debug("Request to partially update PopularQaClickLog : {}", popularQaClickLogDTO);

        return popularQaClickLogRepository
            .findById(popularQaClickLogDTO.getId())
            .map(existingPopularQaClickLog -> {
                popularQaClickLogMapper.partialUpdate(existingPopularQaClickLog, popularQaClickLogDTO);

                return existingPopularQaClickLog;
            })
            .map(popularQaClickLogRepository::save)
            .map(popularQaClickLogMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PopularQaClickLogDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all PopularQaClickLogs");
        return popularQaClickLogRepository.findAll(pageable).map(popularQaClickLogMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PopularQaClickLogDTO> findOne(Long id) {
        LOG.debug("Request to get PopularQaClickLog : {}", id);
        return popularQaClickLogRepository.findById(id).map(popularQaClickLogMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete PopularQaClickLog : {}", id);
        popularQaClickLogRepository.deleteById(id);
    }
}
