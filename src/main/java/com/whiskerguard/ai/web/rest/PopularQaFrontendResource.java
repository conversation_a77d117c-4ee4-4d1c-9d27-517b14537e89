package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.domain.enumeration.PopularQaCategory;
import com.whiskerguard.ai.service.PopularQaClickLogService;
import com.whiskerguard.ai.service.PopularQaService;
import com.whiskerguard.ai.service.dto.PopularQaClickLogDTO;
import com.whiskerguard.ai.service.dto.PopularQaDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.Instant;
import java.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * AI热门问答前端API控制器
 * <p>
 * 专门为前端智能问答首页提供API接口，包括：
 * 1. 常见问题列表
 * 2. 分类浏览
 * 3. 热门推荐
 * 4. 点击统计和聊天初始化
 */
@RestController
@RequestMapping("/api/popular-qa-frontend")
@Tag(name = "PopularQaFrontend", description = "AI热门问答前端API")
public class PopularQaFrontendResource {

    private static final Logger log = LoggerFactory.getLogger(PopularQaFrontendResource.class);

    private static final String ENTITY_NAME = "popularQa";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final PopularQaService popularQaService;
    private final PopularQaClickLogService popularQaClickLogService;

    public PopularQaFrontendResource(
        PopularQaService popularQaService,
        PopularQaClickLogService popularQaClickLogService
    ) {
        this.popularQaService = popularQaService;
        this.popularQaClickLogService = popularQaClickLogService;
    }

    /**
     * 获取常见问题列表
     * <p>
     * GET /api/popular-qa-frontend/common : 获取常见问题列表
     *
     * @param tenantId 租户ID
     * @param pageable 分页参数
     * @return 常见问题列表
     */
    @GetMapping("/common")
    @Operation(
        summary = "获取常见问题列表",
        description = "获取指定租户的常见问题列表，按排序权重和创建时间排序"
    )
    @ApiResponse(
        responseCode = "200",
        description = "成功获取常见问题列表",
        content = @Content(schema = @Schema(implementation = PopularQaDTO.class))
    )
    public ResponseEntity<List<PopularQaDTO>> getCommonQuestions(
        @Parameter(description = "租户ID", required = true) @RequestParam @NotNull Long tenantId,
        @Parameter(description = "分页参数") Pageable pageable
    ) {
        log.debug("REST request to get common questions for tenant: {}", tenantId);
        
        Page<PopularQaDTO> page = popularQaService.findCommonQuestionsByTenant(tenantId, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(
            ServletUriComponentsBuilder.fromCurrentRequest(), 
            page
        );
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 获取所有问答分类
     * <p>
     * GET /api/popular-qa-frontend/categories : 获取所有问答分类
     *
     * @return 分类列表
     */
    @GetMapping("/categories")
    @Operation(
        summary = "获取所有问答分类",
        description = "获取系统支持的所有问答分类枚举值"
    )
    @ApiResponse(
        responseCode = "200",
        description = "成功获取分类列表"
    )
    public ResponseEntity<List<Map<String, String>>> getCategories() {
        log.debug("REST request to get all PopularQa categories");
        
        List<Map<String, String>> categories = new ArrayList<>();
        for (PopularQaCategory category : PopularQaCategory.values()) {
            Map<String, String> categoryMap = new HashMap<>();
            categoryMap.put("key", category.name());
            categoryMap.put("value", category.name()); // 这里可以根据需要添加中文描述
            categories.add(categoryMap);
        }
        
        return ResponseEntity.ok(categories);
    }

    /**
     * 按分类获取问答列表
     * <p>
     * GET /api/popular-qa-frontend/by-category/{category} : 按分类获取问答列表
     *
     * @param category 问答分类
     * @param tenantId 租户ID
     * @param pageable 分页参数
     * @return 指定分类的问答列表
     */
    @GetMapping("/by-category/{category}")
    @Operation(
        summary = "按分类获取问答列表",
        description = "获取指定分类和租户的问答列表"
    )
    @ApiResponse(
        responseCode = "200",
        description = "成功获取分类问答列表",
        content = @Content(schema = @Schema(implementation = PopularQaDTO.class))
    )
    public ResponseEntity<List<PopularQaDTO>> getQuestionsByCategory(
        @Parameter(description = "问答分类", required = true) @PathVariable PopularQaCategory category,
        @Parameter(description = "租户ID", required = true) @RequestParam @NotNull Long tenantId,
        @Parameter(description = "分页参数") Pageable pageable
    ) {
        log.debug("REST request to get questions by category: {} for tenant: {}", category, tenantId);
        
        Page<PopularQaDTO> page = popularQaService.findByTenantAndCategory(tenantId, category, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(
            ServletUriComponentsBuilder.fromCurrentRequest(), 
            page
        );
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 获取热门推荐问答
     * <p>
     * GET /api/popular-qa-frontend/trending : 获取热门推荐问答
     *
     * @param tenantId 租户ID
     * @param pageable 分页参数
     * @return 热门推荐问答列表
     */
    @GetMapping("/trending")
    @Operation(
        summary = "获取热门推荐问答",
        description = "获取指定租户的热门推荐问答，按点击量排序"
    )
    @ApiResponse(
        responseCode = "200",
        description = "成功获取热门推荐列表",
        content = @Content(schema = @Schema(implementation = PopularQaDTO.class))
    )
    public ResponseEntity<List<PopularQaDTO>> getTrendingQuestions(
        @Parameter(description = "租户ID", required = true) @RequestParam @NotNull Long tenantId,
        @Parameter(description = "分页参数") Pageable pageable
    ) {
        log.debug("REST request to get trending questions for tenant: {}", tenantId);
        
        Page<PopularQaDTO> page = popularQaService.findTrendingByTenant(tenantId, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(
            ServletUriComponentsBuilder.fromCurrentRequest(), 
            page
        );
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 点击问答并初始化聊天
     * <p>
     * POST /api/popular-qa-frontend/{id}/start-chat : 点击问答并初始化聊天会话
     *
     * @param id 问答ID
     * @param request HTTP请求对象，用于获取用户IP和User-Agent
     * @param clickRequest 点击请求体，包含租户ID和员工ID
     * @return 聊天初始化响应，包含问答内容和会话ID
     */
    @PostMapping("/{id}/start-chat")
    @Operation(
        summary = "点击问答并初始化聊天",
        description = "记录用户点击行为，返回问答内容，并生成会话ID用于后续聊天"
    )
    @ApiResponse(
        responseCode = "200",
        description = "成功初始化聊天会话",
        content = @Content(schema = @Schema(implementation = PopularQaChatInitResponse.class))
    )
    public ResponseEntity<PopularQaChatInitResponse> startChatWithQuestion(
        @Parameter(description = "问答ID", required = true) @PathVariable @NotNull Long id,
        HttpServletRequest request,
        @Parameter(description = "点击请求体", required = true) @Valid @RequestBody PopularQaClickRequest clickRequest
    ) throws URISyntaxException {
        log.debug("REST request to start chat with PopularQa: {}", id);

        // 1. 获取问答详情
        Optional<PopularQaDTO> popularQaOpt = popularQaService.findOne(id);
        if (popularQaOpt.isEmpty()) {
            throw new BadRequestAlertException("问答不存在", ENTITY_NAME, "idnotfound");
        }

        PopularQaDTO popularQa = popularQaOpt.get();

        // 2. 验证租户权限
        if (!popularQa.getTenantId().equals(clickRequest.getTenantId())) {
            throw new BadRequestAlertException("无权访问该问答", ENTITY_NAME, "tenantmismatch");
        }

        // 3. 生成会话ID
        String conversationId = "qa_" + id + "_" + System.currentTimeMillis();

        // 4. 记录点击日志
        PopularQaClickLogDTO clickLog = new PopularQaClickLogDTO();
        clickLog.setTenantId(clickRequest.getTenantId());
        clickLog.setEmployeeId(clickRequest.getEmployeeId());
        clickLog.setQaId(id);
        clickLog.setClickTime(Instant.now());
        clickLog.setUserIp(getClientIpAddress(request));
        clickLog.setUserAgent(request.getHeader("User-Agent"));
        clickLog.setConversationId(conversationId);

        popularQaClickLogService.save(clickLog);

        // 5. 更新点击次数
        popularQaService.incrementClickCount(id);

        // 6. 构建响应
        PopularQaChatInitResponse response = new PopularQaChatInitResponse();
        response.setConversationId(conversationId);
        response.setQuestionTitle(popularQa.getQuestionTitle());
        response.setQuestionContent(popularQa.getQuestionContent());
        response.setAnswerContent(popularQa.getAnswerContent());
        response.setCategory(popularQa.getCategory());
        response.setTags(popularQa.getTags());

        return ResponseEntity.ok(response);
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 点击请求体
     */
    public static class PopularQaClickRequest {
        @NotNull
        private Long tenantId;

        @NotNull
        private Long employeeId;

        public Long getTenantId() {
            return tenantId;
        }

        public void setTenantId(Long tenantId) {
            this.tenantId = tenantId;
        }

        public Long getEmployeeId() {
            return employeeId;
        }

        public void setEmployeeId(Long employeeId) {
            this.employeeId = employeeId;
        }
    }

    /**
     * 聊天初始化响应
     */
    public static class PopularQaChatInitResponse {
        private String conversationId;
        private String questionTitle;
        private String questionContent;
        private String answerContent;
        private PopularQaCategory category;
        private String tags;

        public String getConversationId() {
            return conversationId;
        }

        public void setConversationId(String conversationId) {
            this.conversationId = conversationId;
        }

        public String getQuestionTitle() {
            return questionTitle;
        }

        public void setQuestionTitle(String questionTitle) {
            this.questionTitle = questionTitle;
        }

        public String getQuestionContent() {
            return questionContent;
        }

        public void setQuestionContent(String questionContent) {
            this.questionContent = questionContent;
        }

        public String getAnswerContent() {
            return answerContent;
        }

        public void setAnswerContent(String answerContent) {
            this.answerContent = answerContent;
        }

        public PopularQaCategory getCategory() {
            return category;
        }

        public void setCategory(PopularQaCategory category) {
            this.category = category;
        }

        public String getTags() {
            return tags;
        }

        public void setTags(String tags) {
            this.tags = tags;
        }
    }
}
