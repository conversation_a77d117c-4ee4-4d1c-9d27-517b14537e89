package com.whiskerguard.ai.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 热门问答点击记录实体
 * 用于统计和分析用户使用情况
 */
@Entity
@Table(name = "popular_qa_click_log")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class PopularQaClickLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户 ID
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 员工 ID
     */
    @NotNull
    @Column(name = "employee_id", nullable = false)
    private Long employeeId;

    /**
     * 问答 ID
     */
    @NotNull
    @Column(name = "qa_id", nullable = false)
    private Long qaId;

    /**
     * 点击时间
     */
    @NotNull
    @Column(name = "click_time", nullable = false)
    private Instant clickTime;

    /**
     * 用户IP
     */
    @Size(max = 45)
    @Column(name = "user_ip", length = 45)
    private String userIp;

    /**
     * 用户代理
     */
    @Size(max = 500)
    @Column(name = "user_agent", length = 500)
    private String userAgent;

    /**
     * 会话ID（关联到后续聊天）
     */
    @Size(max = 64)
    @Column(name = "conversation_id", length = 64)
    private String conversationId;

    @ManyToOne(fetch = FetchType.LAZY)
    private PopularQa qa;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public PopularQaClickLog id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public PopularQaClickLog tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getEmployeeId() {
        return this.employeeId;
    }

    public PopularQaClickLog employeeId(Long employeeId) {
        this.setEmployeeId(employeeId);
        return this;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public Long getQaId() {
        return this.qaId;
    }

    public PopularQaClickLog qaId(Long qaId) {
        this.setQaId(qaId);
        return this;
    }

    public void setQaId(Long qaId) {
        this.qaId = qaId;
    }

    public Instant getClickTime() {
        return this.clickTime;
    }

    public PopularQaClickLog clickTime(Instant clickTime) {
        this.setClickTime(clickTime);
        return this;
    }

    public void setClickTime(Instant clickTime) {
        this.clickTime = clickTime;
    }

    public String getUserIp() {
        return this.userIp;
    }

    public PopularQaClickLog userIp(String userIp) {
        this.setUserIp(userIp);
        return this;
    }

    public void setUserIp(String userIp) {
        this.userIp = userIp;
    }

    public String getUserAgent() {
        return this.userAgent;
    }

    public PopularQaClickLog userAgent(String userAgent) {
        this.setUserAgent(userAgent);
        return this;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getConversationId() {
        return this.conversationId;
    }

    public PopularQaClickLog conversationId(String conversationId) {
        this.setConversationId(conversationId);
        return this;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public PopularQa getQa() {
        return this.qa;
    }

    public void setQa(PopularQa popularQa) {
        this.qa = popularQa;
    }

    public PopularQaClickLog qa(PopularQa popularQa) {
        this.setQa(popularQa);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PopularQaClickLog)) {
            return false;
        }
        return getId() != null && getId().equals(((PopularQaClickLog) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "PopularQaClickLog{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", employeeId=" + getEmployeeId() +
            ", qaId=" + getQaId() +
            ", clickTime='" + getClickTime() + "'" +
            ", userIp='" + getUserIp() + "'" +
            ", userAgent='" + getUserAgent() + "'" +
            ", conversationId='" + getConversationId() + "'" +
            "}";
    }
}
