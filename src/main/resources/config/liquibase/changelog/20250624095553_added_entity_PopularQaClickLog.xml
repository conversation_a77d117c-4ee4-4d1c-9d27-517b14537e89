<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity PopularQaClickLog.
    -->
    <changeSet id="20250624095553-1" author="jhipster">
        <createTable tableName="popular_qa_click_log" remarks="热门问答点击记录实体\n用于统计和分析用户使用情况">
            <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false" />
            </column>
            <column name="employee_id" type="bigint" remarks="员工 ID">
                <constraints nullable="false" />
            </column>
            <column name="qa_id" type="bigint" remarks="问答 ID">
                <constraints nullable="false" />
            </column>
            <column name="click_time" type="${datetimeType}" remarks="点击时间">
                <constraints nullable="false" />
            </column>
            <column name="user_ip" type="varchar(45)" remarks="用户IP">
                <constraints nullable="true" />
            </column>
            <column name="user_agent" type="varchar(500)" remarks="用户代理">
                <constraints nullable="true" />
            </column>
            <column name="conversation_id" type="varchar(64)" remarks="会话ID（关联到后续聊天）">
                <constraints nullable="true" />
            </column>
            <column name="qa_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="popular_qa_click_log" columnName="click_time" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250624095553-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/popular_qa_click_log.csv"
                  separator=";"
                  tableName="popular_qa_click_log"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="employee_id" type="numeric"/>
            <column name="qa_id" type="numeric"/>
            <column name="click_time" type="date"/>
            <column name="user_ip" type="string"/>
            <column name="user_agent" type="string"/>
            <column name="conversation_id" type="string"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
