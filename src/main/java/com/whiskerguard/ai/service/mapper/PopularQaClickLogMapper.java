package com.whiskerguard.ai.service.mapper;

import com.whiskerguard.ai.domain.PopularQa;
import com.whiskerguard.ai.domain.PopularQaClickLog;
import com.whiskerguard.ai.service.dto.PopularQaClickLogDTO;
import com.whiskerguard.ai.service.dto.PopularQaDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link PopularQaClickLog} and its DTO {@link PopularQaClickLogDTO}.
 */
@Mapper(componentModel = "spring")
public interface PopularQaClickLogMapper extends EntityMapper<PopularQaClickLogDTO, PopularQaClickLog> {
    @Mapping(target = "qa", source = "qa", qualifiedByName = "popularQaId")
    @Mapping(target = "qaId", source = "qa.id")
    PopularQaClickLogDTO toDto(PopularQaClickLog s);

    @Mapping(target = "qa", source = "qa")
    @Mapping(target = "qa.id", source = "qaId")
    PopularQaClickLog toEntity(PopularQaClickLogDTO dto);

    @Named("popularQaId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    PopularQaDTO toDtoPopularQaId(PopularQa popularQa);
}
