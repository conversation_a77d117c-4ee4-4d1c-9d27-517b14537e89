package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class PopularQaClickLogTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static PopularQaClickLog getPopularQaClickLogSample1() {
        return new PopularQaClickLog()
            .id(1L)
            .tenantId(1L)
            .employeeId(1L)
            .userIp("userIp1")
            .userAgent("userAgent1")
            .conversationId("conversationId1");
    }

    public static PopularQaClickLog getPopularQaClickLogSample2() {
        return new PopularQaClickLog()
            .id(2L)
            .tenantId(2L)
            .employeeId(2L)
            .userIp("userIp2")
            .userAgent("userAgent2")
            .conversationId("conversationId2");
    }

    public static PopularQaClickLog getPopularQaClickLogRandomSampleGenerator() {
        return new PopularQaClickLog()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .employeeId(longCount.incrementAndGet())
            .userIp(UUID.randomUUID().toString())
            .userAgent(UUID.randomUUID().toString())
            .conversationId(UUID.randomUUID().toString());
    }
}
