package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class PopularQaDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(PopularQaDTO.class);
        PopularQaDTO popularQaDTO1 = new PopularQaDTO();
        popularQaDTO1.setId(1L);
        PopularQaDTO popularQaDTO2 = new PopularQaDTO();
        assertThat(popularQaDTO1).isNotEqualTo(popularQaDTO2);
        popularQaDTO2.setId(popularQaDTO1.getId());
        assertThat(popularQaDTO1).isEqualTo(popularQaDTO2);
        popularQaDTO2.setId(2L);
        assertThat(popularQaDTO1).isNotEqualTo(popularQaDTO2);
        popularQaDTO1.setId(null);
        assertThat(popularQaDTO1).isNotEqualTo(popularQaDTO2);
    }
}
