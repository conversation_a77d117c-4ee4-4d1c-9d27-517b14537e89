package com.whiskerguard.ai.service.mapper;

import com.whiskerguard.ai.domain.PopularQa;
import com.whiskerguard.ai.domain.PopularQaClickLog;
import com.whiskerguard.ai.service.dto.PopularQaClickLogDTO;
import com.whiskerguard.ai.service.dto.PopularQaDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link PopularQaClickLog} and its DTO {@link PopularQaClickLogDTO}.
 */
@Mapper(componentModel = "spring")
public interface PopularQaClickLogMapper extends EntityMapper<PopularQaClickLogDTO, PopularQaClickLog> {
    @Mapping(target = "qa", source = "qa", qualifiedByName = "popularQaId")
    PopularQaClickLogDTO toDto(PopularQaClickLog s);

    @Named("popularQaId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    PopularQaDTO toDtoPopularQaId(PopularQa popularQa);
}
