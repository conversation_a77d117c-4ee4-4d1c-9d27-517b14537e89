package com.whiskerguard.ai.service.impl;

import com.whiskerguard.ai.domain.PopularQa;
import com.whiskerguard.ai.domain.enumeration.PopularQaCategory;
import com.whiskerguard.ai.repository.PopularQaRepository;
import com.whiskerguard.ai.service.PopularQaService;
import com.whiskerguard.ai.service.dto.PopularQaDTO;
import com.whiskerguard.ai.service.mapper.PopularQaMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.ai.domain.PopularQa}.
 */
@Service
@Transactional
public class PopularQaServiceImpl implements PopularQaService {

    private static final Logger LOG = LoggerFactory.getLogger(PopularQaServiceImpl.class);

    private final PopularQaRepository popularQaRepository;

    private final PopularQaMapper popularQaMapper;

    public PopularQaServiceImpl(PopularQaRepository popularQaRepository, PopularQaMapper popularQaMapper) {
        this.popularQaRepository = popularQaRepository;
        this.popularQaMapper = popularQaMapper;
    }

    @Override
    public PopularQaDTO save(PopularQaDTO popularQaDTO) {
        LOG.debug("Request to save PopularQa : {}", popularQaDTO);
        PopularQa popularQa = popularQaMapper.toEntity(popularQaDTO);
        popularQa = popularQaRepository.save(popularQa);
        return popularQaMapper.toDto(popularQa);
    }

    @Override
    public PopularQaDTO update(PopularQaDTO popularQaDTO) {
        LOG.debug("Request to update PopularQa : {}", popularQaDTO);
        PopularQa popularQa = popularQaMapper.toEntity(popularQaDTO);
        popularQa = popularQaRepository.save(popularQa);
        return popularQaMapper.toDto(popularQa);
    }

    @Override
    public Optional<PopularQaDTO> partialUpdate(PopularQaDTO popularQaDTO) {
        LOG.debug("Request to partially update PopularQa : {}", popularQaDTO);

        return popularQaRepository
            .findById(popularQaDTO.getId())
            .map(existingPopularQa -> {
                popularQaMapper.partialUpdate(existingPopularQa, popularQaDTO);

                return existingPopularQa;
            })
            .map(popularQaRepository::save)
            .map(popularQaMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PopularQaDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all PopularQas");
        return popularQaRepository.findAll(pageable).map(popularQaMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PopularQaDTO> findOne(Long id) {
        LOG.debug("Request to get PopularQa : {}", id);
        return popularQaRepository.findById(id).map(popularQaMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete PopularQa : {}", id);
        popularQaRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PopularQaDTO> findCommonQuestionsByTenant(Long tenantId, Pageable pageable) {
        LOG.debug("Request to get common questions for tenant : {}", tenantId);
        return popularQaRepository.findCommonQuestionsByTenant(tenantId, pageable).map(popularQaMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PopularQaDTO> findByTenantAndCategory(Long tenantId, PopularQaCategory category, Pageable pageable) {
        LOG.debug("Request to get questions by tenant : {} and category : {}", tenantId, category);
        return popularQaRepository.findByTenantAndCategory(tenantId, category, pageable).map(popularQaMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PopularQaDTO> findTrendingByTenant(Long tenantId, Pageable pageable) {
        LOG.debug("Request to get trending questions for tenant : {}", tenantId);
        return popularQaRepository.findTrendingByTenant(tenantId, pageable).map(popularQaMapper::toDto);
    }

    @Override
    @Transactional
    public void incrementClickCount(Long id) {
        LOG.debug("Request to increment click count for PopularQa : {}", id);
        popularQaRepository.incrementClickCount(id);
    }
}
