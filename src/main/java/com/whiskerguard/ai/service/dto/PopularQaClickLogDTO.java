package com.whiskerguard.ai.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.ai.domain.PopularQaClickLog} entity.
 */
@Schema(description = "热门问答点击记录实体\n用于统计和分析用户使用情况")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class PopularQaClickLogDTO implements Serializable {

    @Schema(description = "主键 ID")
    private Long id;

    @NotNull
    @Schema(description = "租户 ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @NotNull
    @Schema(description = "员工 ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long employeeId;

    @NotNull
    @Schema(description = "问答 ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long qaId;

    @NotNull
    @Schema(description = "点击时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant clickTime;

    @Size(max = 45)
    @Schema(description = "用户IP")
    private String userIp;

    @Size(max = 500)
    @Schema(description = "用户代理")
    private String userAgent;

    @Size(max = 64)
    @Schema(description = "会话ID（关联到后续聊天）")
    private String conversationId;

    private PopularQaDTO qa;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public Long getQaId() {
        return qaId;
    }

    public void setQaId(Long qaId) {
        this.qaId = qaId;
    }

    public Instant getClickTime() {
        return clickTime;
    }

    public void setClickTime(Instant clickTime) {
        this.clickTime = clickTime;
    }

    public String getUserIp() {
        return userIp;
    }

    public void setUserIp(String userIp) {
        this.userIp = userIp;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public PopularQaDTO getQa() {
        return qa;
    }

    public void setQa(PopularQaDTO qa) {
        this.qa = qa;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PopularQaClickLogDTO)) {
            return false;
        }

        PopularQaClickLogDTO popularQaClickLogDTO = (PopularQaClickLogDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, popularQaClickLogDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "PopularQaClickLogDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", employeeId=" + getEmployeeId() +
            ", qaId=" + getQaId() +
            ", clickTime='" + getClickTime() + "'" +
            ", userIp='" + getUserIp() + "'" +
            ", userAgent='" + getUserAgent() + "'" +
            ", conversationId='" + getConversationId() + "'" +
            ", qa=" + getQa() +
            "}";
    }
}
