package com.whiskerguard.ai.service.dto;

import com.whiskerguard.ai.domain.enumeration.PopularQaCategory;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.ai.domain.PopularQa} entity.
 */
@Schema(description = "AI热门问答实体\n存储企业管理员维护的常见问题和答案")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class PopularQaDTO implements Serializable {

    @Schema(description = "主键 ID")
    private Long id;

    @NotNull
    @Schema(description = "租户 ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @NotNull
    @Size(max = 200)
    @Schema(description = "问题标题", requiredMode = Schema.RequiredMode.REQUIRED)
    private String questionTitle;

    @NotNull
    @Schema(description = "问题内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String questionContent;

    @NotNull
    @Schema(description = "答案内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String answerContent;

    @NotNull
    @Schema(description = "问题分类", requiredMode = Schema.RequiredMode.REQUIRED)
    private PopularQaCategory category;

    @Schema(description = "排序权重（数值越大越靠前）")
    private Integer sortOrder;

    @Schema(description = "点击次数")
    private Long clickCount;

    @NotNull
    @Schema(description = "是否启用", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isEnabled;

    @Schema(description = "是否为热门推荐")
    private Boolean isTrending;

    @Size(max = 500)
    @Schema(description = "标签（多个标签用逗号分隔）")
    private String tags;

    @NotNull
    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Schema(description = "创建者")
    private String createdBy;

    @NotNull
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;

    @Schema(description = "更新者")
    private String updatedBy;

    @NotNull
    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant updatedAt;

    @NotNull
    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getQuestionTitle() {
        return questionTitle;
    }

    public void setQuestionTitle(String questionTitle) {
        this.questionTitle = questionTitle;
    }

    public String getQuestionContent() {
        return questionContent;
    }

    public void setQuestionContent(String questionContent) {
        this.questionContent = questionContent;
    }

    public String getAnswerContent() {
        return answerContent;
    }

    public void setAnswerContent(String answerContent) {
        this.answerContent = answerContent;
    }

    public PopularQaCategory getCategory() {
        return category;
    }

    public void setCategory(PopularQaCategory category) {
        this.category = category;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Long getClickCount() {
        return clickCount;
    }

    public void setClickCount(Long clickCount) {
        this.clickCount = clickCount;
    }

    public Boolean getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }

    public Boolean getIsTrending() {
        return isTrending;
    }

    public void setIsTrending(Boolean isTrending) {
        this.isTrending = isTrending;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PopularQaDTO)) {
            return false;
        }

        PopularQaDTO popularQaDTO = (PopularQaDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, popularQaDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "PopularQaDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", questionTitle='" + getQuestionTitle() + "'" +
            ", questionContent='" + getQuestionContent() + "'" +
            ", answerContent='" + getAnswerContent() + "'" +
            ", category='" + getCategory() + "'" +
            ", sortOrder=" + getSortOrder() +
            ", clickCount=" + getClickCount() +
            ", isEnabled='" + getIsEnabled() + "'" +
            ", isTrending='" + getIsTrending() + "'" +
            ", tags='" + getTags() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
