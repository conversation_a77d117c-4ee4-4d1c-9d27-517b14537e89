package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.PopularQaClickLogRepository;
import com.whiskerguard.ai.service.PopularQaClickLogService;
import com.whiskerguard.ai.service.dto.PopularQaClickLogDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.whiskerguard.ai.domain.PopularQaClickLog}.
 */
@RestController
@RequestMapping("/api/popular-qa-click-logs")
public class PopularQaClickLogResource {

    private static final Logger LOG = LoggerFactory.getLogger(PopularQaClickLogResource.class);

    private static final String ENTITY_NAME = "whiskerguardAiServicePopularQaClickLog";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final PopularQaClickLogService popularQaClickLogService;

    private final PopularQaClickLogRepository popularQaClickLogRepository;

    public PopularQaClickLogResource(
        PopularQaClickLogService popularQaClickLogService,
        PopularQaClickLogRepository popularQaClickLogRepository
    ) {
        this.popularQaClickLogService = popularQaClickLogService;
        this.popularQaClickLogRepository = popularQaClickLogRepository;
    }

    /**
     * {@code POST  /popular-qa-click-logs} : Create a new popularQaClickLog.
     *
     * @param popularQaClickLogDTO the popularQaClickLogDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new popularQaClickLogDTO, or with status {@code 400 (Bad Request)} if the popularQaClickLog has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<PopularQaClickLogDTO> createPopularQaClickLog(@Valid @RequestBody PopularQaClickLogDTO popularQaClickLogDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save PopularQaClickLog : {}", popularQaClickLogDTO);
        if (popularQaClickLogDTO.getId() != null) {
            throw new BadRequestAlertException("A new popularQaClickLog cannot already have an ID", ENTITY_NAME, "idexists");
        }
        popularQaClickLogDTO = popularQaClickLogService.save(popularQaClickLogDTO);
        return ResponseEntity.created(new URI("/api/popular-qa-click-logs/" + popularQaClickLogDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, popularQaClickLogDTO.getId().toString()))
            .body(popularQaClickLogDTO);
    }

    /**
     * {@code PUT  /popular-qa-click-logs/:id} : Updates an existing popularQaClickLog.
     *
     * @param id the id of the popularQaClickLogDTO to save.
     * @param popularQaClickLogDTO the popularQaClickLogDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated popularQaClickLogDTO,
     * or with status {@code 400 (Bad Request)} if the popularQaClickLogDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the popularQaClickLogDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<PopularQaClickLogDTO> updatePopularQaClickLog(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody PopularQaClickLogDTO popularQaClickLogDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update PopularQaClickLog : {}, {}", id, popularQaClickLogDTO);
        if (popularQaClickLogDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, popularQaClickLogDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!popularQaClickLogRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        popularQaClickLogDTO = popularQaClickLogService.update(popularQaClickLogDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, popularQaClickLogDTO.getId().toString()))
            .body(popularQaClickLogDTO);
    }

    /**
     * {@code PATCH  /popular-qa-click-logs/:id} : Partial updates given fields of an existing popularQaClickLog, field will ignore if it is null
     *
     * @param id the id of the popularQaClickLogDTO to save.
     * @param popularQaClickLogDTO the popularQaClickLogDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated popularQaClickLogDTO,
     * or with status {@code 400 (Bad Request)} if the popularQaClickLogDTO is not valid,
     * or with status {@code 404 (Not Found)} if the popularQaClickLogDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the popularQaClickLogDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<PopularQaClickLogDTO> partialUpdatePopularQaClickLog(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody PopularQaClickLogDTO popularQaClickLogDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update PopularQaClickLog partially : {}, {}", id, popularQaClickLogDTO);
        if (popularQaClickLogDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, popularQaClickLogDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!popularQaClickLogRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<PopularQaClickLogDTO> result = popularQaClickLogService.partialUpdate(popularQaClickLogDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, popularQaClickLogDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /popular-qa-click-logs} : get all the popularQaClickLogs.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of popularQaClickLogs in body.
     */
    @GetMapping("")
    public ResponseEntity<List<PopularQaClickLogDTO>> getAllPopularQaClickLogs(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of PopularQaClickLogs");
        Page<PopularQaClickLogDTO> page = popularQaClickLogService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /popular-qa-click-logs/:id} : get the "id" popularQaClickLog.
     *
     * @param id the id of the popularQaClickLogDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the popularQaClickLogDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<PopularQaClickLogDTO> getPopularQaClickLog(@PathVariable("id") Long id) {
        LOG.debug("REST request to get PopularQaClickLog : {}", id);
        Optional<PopularQaClickLogDTO> popularQaClickLogDTO = popularQaClickLogService.findOne(id);
        return ResponseUtil.wrapOrNotFound(popularQaClickLogDTO);
    }

    /**
     * {@code DELETE  /popular-qa-click-logs/:id} : delete the "id" popularQaClickLog.
     *
     * @param id the id of the popularQaClickLogDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePopularQaClickLog(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete PopularQaClickLog : {}", id);
        popularQaClickLogService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
