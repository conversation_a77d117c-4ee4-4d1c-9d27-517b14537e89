<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity PopularQa.
    -->
    <changeSet id="20250624095552-1" author="jhipster">
        <createTable tableName="popular_qa" remarks="AI热门问答实体\n存储企业管理员维护的常见问题和答案">
            <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false" />
            </column>
            <column name="question_title" type="varchar(200)" remarks="问题标题">
                <constraints nullable="false" />
            </column>
            <column name="question_content" type="varchar(255)" remarks="问题内容">
                <constraints nullable="false" />
            </column>
            <column name="answer_content" type="varchar(255)" remarks="答案内容">
                <constraints nullable="false" />
            </column>
            <column name="category" type="varchar(255)" remarks="问题分类">
                <constraints nullable="false" />
            </column>
            <column name="sort_order" type="integer" remarks="排序权重（数值越大越靠前）">
                <constraints nullable="true" />
            </column>
            <column name="click_count" type="bigint" remarks="点击次数">
                <constraints nullable="true" />
            </column>
            <column name="is_enabled" type="boolean" remarks="是否启用">
                <constraints nullable="false" />
            </column>
            <column name="is_trending" type="boolean" remarks="是否为热门推荐">
                <constraints nullable="true" />
            </column>
            <column name="tags" type="varchar(500)" remarks="标签（多个标签用逗号分隔）">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(255)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(255)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="popular_qa" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="popular_qa" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250624095552-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/popular_qa.csv"
                  separator=";"
                  tableName="popular_qa"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="question_title" type="string"/>
            <column name="question_content" type="string"/>
            <column name="answer_content" type="string"/>
            <column name="category" type="string"/>
            <column name="sort_order" type="numeric"/>
            <column name="click_count" type="numeric"/>
            <column name="is_enabled" type="boolean"/>
            <column name="is_trending" type="boolean"/>
            <column name="tags" type="string"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
