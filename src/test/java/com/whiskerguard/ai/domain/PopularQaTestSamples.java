package com.whiskerguard.ai.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class PopularQaTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static PopularQa getPopularQaSample1() {
        return new PopularQa()
            .id(1L)
            .tenantId(1L)
            .questionTitle("questionTitle1")
            .questionContent("questionContent1")
            .answerContent("answerContent1")
            .sortOrder(1)
            .clickCount(1L)
            .tags("tags1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static PopularQa getPopularQaSample2() {
        return new PopularQa()
            .id(2L)
            .tenantId(2L)
            .questionTitle("questionTitle2")
            .questionContent("questionContent2")
            .answerContent("answerContent2")
            .sortOrder(2)
            .clickCount(2L)
            .tags("tags2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static PopularQa getPopularQaRandomSampleGenerator() {
        return new PopularQa()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .questionTitle(UUID.randomUUID().toString())
            .questionContent(UUID.randomUUID().toString())
            .answerContent(UUID.randomUUID().toString())
            .sortOrder(intCount.incrementAndGet())
            .clickCount(longCount.incrementAndGet())
            .tags(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
