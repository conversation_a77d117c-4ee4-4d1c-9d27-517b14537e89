package com.whiskerguard.ai.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class PopularQaClickLogDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(PopularQaClickLogDTO.class);
        PopularQaClickLogDTO popularQaClickLogDTO1 = new PopularQaClickLogDTO();
        popularQaClickLogDTO1.setId(1L);
        PopularQaClickLogDTO popularQaClickLogDTO2 = new PopularQaClickLogDTO();
        assertThat(popularQaClickLogDTO1).isNotEqualTo(popularQaClickLogDTO2);
        popularQaClickLogDTO2.setId(popularQaClickLogDTO1.getId());
        assertThat(popularQaClickLogDTO1).isEqualTo(popularQaClickLogDTO2);
        popularQaClickLogDTO2.setId(2L);
        assertThat(popularQaClickLogDTO1).isNotEqualTo(popularQaClickLogDTO2);
        popularQaClickLogDTO1.setId(null);
        assertThat(popularQaClickLogDTO1).isNotEqualTo(popularQaClickLogDTO2);
    }
}
