package com.whiskerguard.ai.service;

import com.whiskerguard.ai.service.dto.PopularQaClickLogDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.ai.domain.PopularQaClickLog}.
 */
public interface PopularQaClickLogService {
    /**
     * Save a popularQaClickLog.
     *
     * @param popularQaClickLogDTO the entity to save.
     * @return the persisted entity.
     */
    PopularQaClickLogDTO save(PopularQaClickLogDTO popularQaClickLogDTO);

    /**
     * Updates a popularQaClickLog.
     *
     * @param popularQaClickLogDTO the entity to update.
     * @return the persisted entity.
     */
    PopularQaClickLogDTO update(PopularQaClickLogDTO popularQaClickLogDTO);

    /**
     * Partially updates a popularQaClickLog.
     *
     * @param popularQaClickLogDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<PopularQaClickLogDTO> partialUpdate(PopularQaClickLogDTO popularQaClickLogDTO);

    /**
     * Get all the popularQaClickLogs.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<PopularQaClickLogDTO> findAll(Pageable pageable);

    /**
     * Get the "id" popularQaClickLog.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<PopularQaClickLogDTO> findOne(Long id);

    /**
     * Delete the "id" popularQaClickLog.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
