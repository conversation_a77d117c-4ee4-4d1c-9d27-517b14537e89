package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.PopularQaRepository;
import com.whiskerguard.ai.service.PopularQaService;
import com.whiskerguard.ai.service.dto.PopularQaDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.whiskerguard.ai.domain.PopularQa}.
 */
@RestController
@RequestMapping("/api/popular-qas")
public class PopularQaResource {

    private static final Logger LOG = LoggerFactory.getLogger(PopularQaResource.class);

    private static final String ENTITY_NAME = "whiskerguardAiServicePopularQa";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final PopularQaService popularQaService;

    private final PopularQaRepository popularQaRepository;

    public PopularQaResource(PopularQaService popularQaService, PopularQaRepository popularQaRepository) {
        this.popularQaService = popularQaService;
        this.popularQaRepository = popularQaRepository;
    }

    /**
     * {@code POST  /popular-qas} : Create a new popularQa.
     *
     * @param popularQaDTO the popularQaDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new popularQaDTO, or with status {@code 400 (Bad Request)} if the popularQa has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<PopularQaDTO> createPopularQa(@Valid @RequestBody PopularQaDTO popularQaDTO) throws URISyntaxException {
        LOG.debug("REST request to save PopularQa : {}", popularQaDTO);
        if (popularQaDTO.getId() != null) {
            throw new BadRequestAlertException("A new popularQa cannot already have an ID", ENTITY_NAME, "idexists");
        }
        popularQaDTO = popularQaService.save(popularQaDTO);
        return ResponseEntity.created(new URI("/api/popular-qas/" + popularQaDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, popularQaDTO.getId().toString()))
            .body(popularQaDTO);
    }

    /**
     * {@code PUT  /popular-qas/:id} : Updates an existing popularQa.
     *
     * @param id the id of the popularQaDTO to save.
     * @param popularQaDTO the popularQaDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated popularQaDTO,
     * or with status {@code 400 (Bad Request)} if the popularQaDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the popularQaDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<PopularQaDTO> updatePopularQa(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody PopularQaDTO popularQaDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update PopularQa : {}, {}", id, popularQaDTO);
        if (popularQaDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, popularQaDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!popularQaRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        popularQaDTO = popularQaService.update(popularQaDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, popularQaDTO.getId().toString()))
            .body(popularQaDTO);
    }

    /**
     * {@code PATCH  /popular-qas/:id} : Partial updates given fields of an existing popularQa, field will ignore if it is null
     *
     * @param id the id of the popularQaDTO to save.
     * @param popularQaDTO the popularQaDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated popularQaDTO,
     * or with status {@code 400 (Bad Request)} if the popularQaDTO is not valid,
     * or with status {@code 404 (Not Found)} if the popularQaDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the popularQaDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<PopularQaDTO> partialUpdatePopularQa(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody PopularQaDTO popularQaDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update PopularQa partially : {}, {}", id, popularQaDTO);
        if (popularQaDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, popularQaDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!popularQaRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<PopularQaDTO> result = popularQaService.partialUpdate(popularQaDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, popularQaDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /popular-qas} : get all the popularQas.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of popularQas in body.
     */
    @GetMapping("")
    public ResponseEntity<List<PopularQaDTO>> getAllPopularQas(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of PopularQas");
        Page<PopularQaDTO> page = popularQaService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /popular-qas/:id} : get the "id" popularQa.
     *
     * @param id the id of the popularQaDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the popularQaDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<PopularQaDTO> getPopularQa(@PathVariable("id") Long id) {
        LOG.debug("REST request to get PopularQa : {}", id);
        Optional<PopularQaDTO> popularQaDTO = popularQaService.findOne(id);
        return ResponseUtil.wrapOrNotFound(popularQaDTO);
    }

    /**
     * {@code DELETE  /popular-qas/:id} : delete the "id" popularQa.
     *
     * @param id the id of the popularQaDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePopularQa(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete PopularQa : {}", id);
        popularQaService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
