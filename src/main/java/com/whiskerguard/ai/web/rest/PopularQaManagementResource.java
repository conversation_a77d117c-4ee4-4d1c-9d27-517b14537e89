package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.domain.enumeration.PopularQaCategory;
import com.whiskerguard.ai.service.PopularQaClickLogService;
import com.whiskerguard.ai.service.PopularQaService;
import com.whiskerguard.ai.service.dto.PopularQaClickLogDTO;
import com.whiskerguard.ai.service.dto.PopularQaDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.Instant;
import java.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * AI热门问答管理员后台API控制器
 * <p>
 * 专门为企业管理员提供热门问答的CRUD管理功能，包括：
 * 1. 问答的增删改查
 * 2. 点击统计查看
 * 3. 数据分析报表
 * 4. 批量操作
 */
@RestController
@RequestMapping("/api/admin/popular-qa")
@Tag(name = "PopularQaManagement", description = "AI热门问答管理员API")
@SecurityRequirement(name = "bearerAuth")
public class PopularQaManagementResource {

    private static final Logger log = LoggerFactory.getLogger(PopularQaManagementResource.class);

    private static final String ENTITY_NAME = "popularQa";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final PopularQaService popularQaService;
    private final PopularQaClickLogService popularQaClickLogService;

    public PopularQaManagementResource(
        PopularQaService popularQaService,
        PopularQaClickLogService popularQaClickLogService
    ) {
        this.popularQaService = popularQaService;
        this.popularQaClickLogService = popularQaClickLogService;
    }

    /**
     * 创建新的热门问答
     * <p>
     * POST /api/admin/popular-qa : 创建新的热门问答
     *
     * @param popularQaDTO 要创建的热门问答
     * @return 创建成功的热门问答
     */
    @PostMapping("")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    @Operation(
        summary = "创建新的热门问答",
        description = "管理员创建新的热门问答条目"
    )
    @ApiResponse(
        responseCode = "201",
        description = "成功创建热门问答",
        content = @Content(schema = @Schema(implementation = PopularQaDTO.class))
    )
    public ResponseEntity<PopularQaDTO> createPopularQa(
        @Parameter(description = "热门问答数据", required = true) @Valid @RequestBody PopularQaDTO popularQaDTO
    ) throws URISyntaxException {
        log.debug("REST request to save PopularQa : {}", popularQaDTO);
        
        if (popularQaDTO.getId() != null) {
            throw new BadRequestAlertException("新创建的问答不能包含ID", ENTITY_NAME, "idexists");
        }
        
        // 设置默认值
        if (popularQaDTO.getClickCount() == null) {
            popularQaDTO.setClickCount(0L);
        }
        if (popularQaDTO.getIsEnabled() == null) {
            popularQaDTO.setIsEnabled(true);
        }
        if (popularQaDTO.getIsTrending() == null) {
            popularQaDTO.setIsTrending(false);
        }
        if (popularQaDTO.getIsDeleted() == null) {
            popularQaDTO.setIsDeleted(false);
        }
        if (popularQaDTO.getVersion() == null) {
            popularQaDTO.setVersion(1);
        }
        
        // 设置时间戳
        Instant now = Instant.now();
        popularQaDTO.setCreatedAt(now);
        popularQaDTO.setUpdatedAt(now);
        
        PopularQaDTO result = popularQaService.save(popularQaDTO);
        
        return ResponseEntity.created(new URI("/api/admin/popular-qa/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * 更新热门问答
     * <p>
     * PUT /api/admin/popular-qa/{id} : 更新指定的热门问答
     *
     * @param id 问答ID
     * @param popularQaDTO 要更新的热门问答数据
     * @return 更新后的热门问答
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    @Operation(
        summary = "更新热门问答",
        description = "管理员更新指定的热门问答"
    )
    @ApiResponse(
        responseCode = "200",
        description = "成功更新热门问答",
        content = @Content(schema = @Schema(implementation = PopularQaDTO.class))
    )
    public ResponseEntity<PopularQaDTO> updatePopularQa(
        @Parameter(description = "问答ID", required = true) @PathVariable(value = "id", required = false) final Long id,
        @Parameter(description = "热门问答数据", required = true) @Valid @RequestBody PopularQaDTO popularQaDTO
    ) throws URISyntaxException {
        log.debug("REST request to update PopularQa : {}, {}", id, popularQaDTO);
        
        if (popularQaDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, popularQaDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!popularQaService.findOne(id).isPresent()) {
            throw new BadRequestAlertException("实体不存在", ENTITY_NAME, "idnotfound");
        }

        // 更新时间戳
        popularQaDTO.setUpdatedAt(Instant.now());
        
        PopularQaDTO result = popularQaService.update(popularQaDTO);
        
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, popularQaDTO.getId().toString()))
            .body(result);
    }

    /**
     * 获取所有热门问答
     * <p>
     * GET /api/admin/popular-qa : 获取所有热门问答列表
     *
     * @param pageable 分页参数
     * @return 热门问答分页列表
     */
    @GetMapping("")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    @Operation(
        summary = "获取所有热门问答",
        description = "管理员获取所有热门问答列表（包括已删除的）"
    )
    @ApiResponse(
        responseCode = "200",
        description = "成功获取热门问答列表",
        content = @Content(schema = @Schema(implementation = PopularQaDTO.class))
    )
    public ResponseEntity<List<PopularQaDTO>> getAllPopularQas(
        @Parameter(description = "分页参数") Pageable pageable
    ) {
        log.debug("REST request to get a page of PopularQas");
        
        Page<PopularQaDTO> page = popularQaService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(
            ServletUriComponentsBuilder.fromCurrentRequest(), 
            page
        );
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 获取指定热门问答
     * <p>
     * GET /api/admin/popular-qa/{id} : 获取指定ID的热门问答
     *
     * @param id 问答ID
     * @return 热门问答详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    @Operation(
        summary = "获取指定热门问答",
        description = "管理员获取指定ID的热门问答详情"
    )
    @ApiResponse(
        responseCode = "200",
        description = "成功获取热门问答详情",
        content = @Content(schema = @Schema(implementation = PopularQaDTO.class))
    )
    public ResponseEntity<PopularQaDTO> getPopularQa(
        @Parameter(description = "问答ID", required = true) @PathVariable("id") Long id
    ) {
        log.debug("REST request to get PopularQa : {}", id);
        
        Optional<PopularQaDTO> popularQaDTO = popularQaService.findOne(id);
        return ResponseUtil.wrapOrNotFound(popularQaDTO);
    }

    /**
     * 删除热门问答
     * <p>
     * DELETE /api/admin/popular-qa/{id} : 删除指定的热门问答
     *
     * @param id 问答ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    @Operation(
        summary = "删除热门问答",
        description = "管理员删除指定的热门问答（软删除）"
    )
    @ApiResponse(
        responseCode = "204",
        description = "成功删除热门问答"
    )
    public ResponseEntity<Void> deletePopularQa(
        @Parameter(description = "问答ID", required = true) @PathVariable("id") Long id
    ) {
        log.debug("REST request to delete PopularQa : {}", id);
        
        // 这里应该实现软删除，而不是物理删除
        // 暂时使用物理删除，后续可以改为软删除
        popularQaService.delete(id);
        
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
