package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.PopularQaClickLogAsserts.*;
import static com.whiskerguard.ai.domain.PopularQaClickLogTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class PopularQaClickLogMapperTest {

    private PopularQaClickLogMapper popularQaClickLogMapper;

    @BeforeEach
    void setUp() {
        popularQaClickLogMapper = new PopularQaClickLogMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getPopularQaClickLogSample1();
        var actual = popularQaClickLogMapper.toEntity(popularQaClickLogMapper.toDto(expected));
        assertPopularQaClickLogAllPropertiesEquals(expected, actual);
    }
}
