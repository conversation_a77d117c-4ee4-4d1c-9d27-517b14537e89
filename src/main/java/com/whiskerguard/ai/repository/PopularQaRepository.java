package com.whiskerguard.ai.repository;

import com.whiskerguard.ai.domain.PopularQa;
import com.whiskerguard.ai.domain.enumeration.PopularQaCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the PopularQa entity.
 */
@SuppressWarnings("unused")
@Repository
public interface PopularQaRepository extends JpaRepository<PopularQa, Long> {

    /**
     * 根据租户ID查找启用的常见问题
     * 按排序权重降序和创建时间降序排列
     */
    @Query("SELECT p FROM PopularQa p WHERE p.tenantId = :tenantId AND p.isEnabled = true AND p.isDeleted = false " +
           "ORDER BY p.sortOrder DESC NULLS LAST, p.createdAt DESC")
    Page<PopularQa> findCommonQuestionsByTenant(@Param("tenantId") Long tenantId, Pageable pageable);

    /**
     * 根据租户ID和分类查找启用的问答
     * 按排序权重降序和创建时间降序排列
     */
    @Query("SELECT p FROM PopularQa p WHERE p.tenantId = :tenantId AND p.category = :category " +
           "AND p.isEnabled = true AND p.isDeleted = false " +
           "ORDER BY p.sortOrder DESC NULLS LAST, p.createdAt DESC")
    Page<PopularQa> findByTenantAndCategory(@Param("tenantId") Long tenantId,
                                           @Param("category") PopularQaCategory category,
                                           Pageable pageable);

    /**
     * 根据租户ID查找热门推荐问答
     * 按点击量降序排列
     */
    @Query("SELECT p FROM PopularQa p WHERE p.tenantId = :tenantId AND p.isTrending = true " +
           "AND p.isEnabled = true AND p.isDeleted = false " +
           "ORDER BY p.clickCount DESC NULLS LAST, p.createdAt DESC")
    Page<PopularQa> findTrendingByTenant(@Param("tenantId") Long tenantId, Pageable pageable);

    /**
     * 原子性地增加点击次数
     */
    @Modifying
    @Query("UPDATE PopularQa p SET p.clickCount = COALESCE(p.clickCount, 0) + 1 WHERE p.id = :id")
    void incrementClickCount(@Param("id") Long id);
}
