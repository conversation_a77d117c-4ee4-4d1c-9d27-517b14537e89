package com.whiskerguard.ai.service.mapper;

import static com.whiskerguard.ai.domain.PopularQaAsserts.*;
import static com.whiskerguard.ai.domain.PopularQaTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class PopularQaMapperTest {

    private PopularQaMapper popularQaMapper;

    @BeforeEach
    void setUp() {
        popularQaMapper = new PopularQaMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getPopularQaSample1();
        var actual = popularQaMapper.toEntity(popularQaMapper.toDto(expected));
        assertPopularQaAllPropertiesEquals(expected, actual);
    }
}
