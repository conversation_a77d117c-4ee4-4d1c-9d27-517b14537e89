package com.whiskerguard.ai.web.rest;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.PopularQa;
import com.whiskerguard.ai.domain.enumeration.PopularQaCategory;
import com.whiskerguard.ai.repository.PopularQaRepository;
import com.whiskerguard.ai.service.dto.PopularQaDTO;
import com.whiskerguard.ai.service.mapper.PopularQaMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * AI热门问答前端API集成测试
 */
@IntegrationTest
@AutoConfigureWebMvc
@WithMockUser
class PopularQaFrontendResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_QUESTION_TITLE = "如何提交合同审查申请？";
    private static final String UPDATED_QUESTION_TITLE = "最新培训计划是什么？";

    private static final String DEFAULT_QUESTION_CONTENT = "我想提交后多久能收到审查结果？";
    private static final String UPDATED_QUESTION_CONTENT = "公司最新的合规培训计划包含哪些内容？";

    private static final String DEFAULT_ANSWER_CONTENT = "您可以通过合同审查模块提交申请，一般情况下，系统会在2-4个工作小时内完成AI智能审查并返回结果。";
    private static final String UPDATED_ANSWER_CONTENT = "最新的合规培训计划包括：1）法律法规更新培训；2）内控制度解读；3）风险识别与防控；4）案例分析与讨论。";

    private static final PopularQaCategory DEFAULT_CATEGORY = PopularQaCategory.CONTRACT_REVIEW;
    private static final PopularQaCategory UPDATED_CATEGORY = PopularQaCategory.POLICY_REVIEW;

    private static final Integer DEFAULT_SORT_ORDER = 100;
    private static final Integer UPDATED_SORT_ORDER = 90;

    private static final Long DEFAULT_CLICK_COUNT = 0L;
    private static final Long UPDATED_CLICK_COUNT = 1L;

    private static final Boolean DEFAULT_IS_ENABLED = true;
    private static final Boolean UPDATED_IS_ENABLED = false;

    private static final Boolean DEFAULT_IS_TRENDING = true;
    private static final Boolean UPDATED_IS_TRENDING = false;

    private static final String DEFAULT_TAGS = "合同,审查,申请";
    private static final String UPDATED_TAGS = "培训,合规,计划";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "system";
    private static final String UPDATED_CREATED_BY = "admin";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "system";
    private static final String UPDATED_UPDATED_BY = "admin";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/popular-qa-frontend";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private PopularQaRepository popularQaRepository;

    @Autowired
    private PopularQaMapper popularQaMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restPopularQaFrontendMockMvc;

    private PopularQa popularQa;

    /**
     * 创建测试用的PopularQa实体
     */
    public static PopularQa createEntity(EntityManager em) {
        PopularQa popularQa = new PopularQa()
            .tenantId(DEFAULT_TENANT_ID)
            .questionTitle(DEFAULT_QUESTION_TITLE)
            .questionContent(DEFAULT_QUESTION_CONTENT)
            .answerContent(DEFAULT_ANSWER_CONTENT)
            .category(DEFAULT_CATEGORY)
            .sortOrder(DEFAULT_SORT_ORDER)
            .clickCount(DEFAULT_CLICK_COUNT)
            .isEnabled(DEFAULT_IS_ENABLED)
            .isTrending(DEFAULT_IS_TRENDING)
            .tags(DEFAULT_TAGS)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
        return popularQa;
    }

    /**
     * 创建更新后的PopularQa实体
     */
    public static PopularQa createUpdatedEntity(EntityManager em) {
        PopularQa popularQa = new PopularQa()
            .tenantId(UPDATED_TENANT_ID)
            .questionTitle(UPDATED_QUESTION_TITLE)
            .questionContent(UPDATED_QUESTION_CONTENT)
            .answerContent(UPDATED_ANSWER_CONTENT)
            .category(UPDATED_CATEGORY)
            .sortOrder(UPDATED_SORT_ORDER)
            .clickCount(UPDATED_CLICK_COUNT)
            .isEnabled(UPDATED_IS_ENABLED)
            .isTrending(UPDATED_IS_TRENDING)
            .tags(UPDATED_TAGS)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        return popularQa;
    }

    @BeforeEach
    void initTest() {
        popularQa = createEntity(em);
    }

    @Test
    @Transactional
    void getCommonQuestions() throws Exception {
        // 初始化数据库
        popularQaRepository.saveAndFlush(popularQa);

        // 获取常见问题列表
        restPopularQaFrontendMockMvc
            .perform(get(ENTITY_API_URL + "/common?tenantId=" + DEFAULT_TENANT_ID))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(popularQa.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].questionTitle").value(hasItem(DEFAULT_QUESTION_TITLE)))
            .andExpect(jsonPath("$.[*].questionContent").value(hasItem(DEFAULT_QUESTION_CONTENT)))
            .andExpect(jsonPath("$.[*].answerContent").value(hasItem(DEFAULT_ANSWER_CONTENT)))
            .andExpect(jsonPath("$.[*].category").value(hasItem(DEFAULT_CATEGORY.toString())))
            .andExpect(jsonPath("$.[*].sortOrder").value(hasItem(DEFAULT_SORT_ORDER)))
            .andExpect(jsonPath("$.[*].clickCount").value(hasItem(DEFAULT_CLICK_COUNT.intValue())))
            .andExpect(jsonPath("$.[*].isEnabled").value(hasItem(DEFAULT_IS_ENABLED)))
            .andExpect(jsonPath("$.[*].isTrending").value(hasItem(DEFAULT_IS_TRENDING)))
            .andExpect(jsonPath("$.[*].tags").value(hasItem(DEFAULT_TAGS)));
    }

    @Test
    @Transactional
    void getCategories() throws Exception {
        // 获取所有分类
        restPopularQaFrontendMockMvc
            .perform(get(ENTITY_API_URL + "/categories"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$").isArray())
            .andExpect(jsonPath("$.length()").value(PopularQaCategory.values().length));
    }

    @Test
    @Transactional
    void getQuestionsByCategory() throws Exception {
        // 初始化数据库
        popularQaRepository.saveAndFlush(popularQa);

        // 按分类获取问答列表
        restPopularQaFrontendMockMvc
            .perform(get(ENTITY_API_URL + "/by-category/" + DEFAULT_CATEGORY + "?tenantId=" + DEFAULT_TENANT_ID))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(popularQa.getId().intValue())))
            .andExpect(jsonPath("$.[*].category").value(hasItem(DEFAULT_CATEGORY.toString())));
    }

    @Test
    @Transactional
    void getTrendingQuestions() throws Exception {
        // 初始化数据库
        popularQaRepository.saveAndFlush(popularQa);

        // 获取热门推荐问答
        restPopularQaFrontendMockMvc
            .perform(get(ENTITY_API_URL + "/trending?tenantId=" + DEFAULT_TENANT_ID))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(popularQa.getId().intValue())))
            .andExpect(jsonPath("$.[*].isTrending").value(hasItem(DEFAULT_IS_TRENDING)));
    }

    @Test
    @Transactional
    void startChatWithQuestion() throws Exception {
        // 初始化数据库
        popularQaRepository.saveAndFlush(popularQa);

        // 创建点击请求
        var clickRequest = new PopularQaFrontendResource.PopularQaClickRequest();
        clickRequest.setTenantId(DEFAULT_TENANT_ID);
        clickRequest.setEmployeeId(1L);

        // 点击问答并初始化聊天
        restPopularQaFrontendMockMvc
            .perform(
                post(ENTITY_API_URL + "/{id}/start-chat", popularQa.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(clickRequest))
            )
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.conversationId").exists())
            .andExpect(jsonPath("$.questionTitle").value(DEFAULT_QUESTION_TITLE))
            .andExpect(jsonPath("$.questionContent").value(DEFAULT_QUESTION_CONTENT))
            .andExpect(jsonPath("$.answerContent").value(DEFAULT_ANSWER_CONTENT))
            .andExpect(jsonPath("$.category").value(DEFAULT_CATEGORY.toString()))
            .andExpect(jsonPath("$.tags").value(DEFAULT_TAGS));

        // 验证点击次数是否增加
        List<PopularQa> popularQaList = popularQaRepository.findAll();
        assertThat(popularQaList).hasSize(1);
        PopularQa testPopularQa = popularQaList.get(0);
        assertThat(testPopularQa.getClickCount()).isEqualTo(DEFAULT_CLICK_COUNT + 1);
    }

    @Test
    @Transactional
    void startChatWithQuestionNonExistingId() throws Exception {
        // 创建点击请求
        var clickRequest = new PopularQaFrontendResource.PopularQaClickRequest();
        clickRequest.setTenantId(DEFAULT_TENANT_ID);
        clickRequest.setEmployeeId(1L);

        // 尝试点击不存在的问答
        restPopularQaFrontendMockMvc
            .perform(
                post(ENTITY_API_URL + "/{id}/start-chat", Long.MAX_VALUE)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(clickRequest))
            )
            .andExpect(status().isBadRequest());
    }

    @Test
    @Transactional
    void startChatWithQuestionTenantMismatch() throws Exception {
        // 初始化数据库
        popularQaRepository.saveAndFlush(popularQa);

        // 创建不同租户的点击请求
        var clickRequest = new PopularQaFrontendResource.PopularQaClickRequest();
        clickRequest.setTenantId(999L); // 不同的租户ID
        clickRequest.setEmployeeId(1L);

        // 尝试点击其他租户的问答
        restPopularQaFrontendMockMvc
            .perform(
                post(ENTITY_API_URL + "/{id}/start-chat", popularQa.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(TestUtil.convertObjectToJsonBytes(clickRequest))
            )
            .andExpect(status().isBadRequest());
    }
}
