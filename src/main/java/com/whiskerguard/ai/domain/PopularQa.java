package com.whiskerguard.ai.domain;

import com.whiskerguard.ai.domain.enumeration.PopularQaCategory;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * AI热门问答实体
 * 存储企业管理员维护的常见问题和答案
 */
@Entity
@Table(name = "popular_qa")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class PopularQa implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户 ID
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 问题标题
     */
    @NotNull
    @Size(max = 200)
    @Column(name = "question_title", length = 200, nullable = false)
    private String questionTitle;

    /**
     * 问题内容
     */
    @NotNull
    @Column(name = "question_content", nullable = false)
    private String questionContent;

    /**
     * 答案内容
     */
    @NotNull
    @Column(name = "answer_content", nullable = false)
    private String answerContent;

    /**
     * 问题分类
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "category", nullable = false)
    private PopularQaCategory category;

    /**
     * 排序权重（数值越大越靠前）
     */
    @Column(name = "sort_order")
    private Integer sortOrder;

    /**
     * 点击次数
     */
    @Column(name = "click_count")
    private Long clickCount;

    /**
     * 是否启用
     */
    @NotNull
    @Column(name = "is_enabled", nullable = false)
    private Boolean isEnabled;

    /**
     * 是否为热门推荐
     */
    @Column(name = "is_trending")
    private Boolean isTrending;

    /**
     * 标签（多个标签用逗号分隔）
     */
    @Size(max = 500)
    @Column(name = "tags", length = 500)
    private String tags;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public PopularQa id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public PopularQa tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getQuestionTitle() {
        return this.questionTitle;
    }

    public PopularQa questionTitle(String questionTitle) {
        this.setQuestionTitle(questionTitle);
        return this;
    }

    public void setQuestionTitle(String questionTitle) {
        this.questionTitle = questionTitle;
    }

    public String getQuestionContent() {
        return this.questionContent;
    }

    public PopularQa questionContent(String questionContent) {
        this.setQuestionContent(questionContent);
        return this;
    }

    public void setQuestionContent(String questionContent) {
        this.questionContent = questionContent;
    }

    public String getAnswerContent() {
        return this.answerContent;
    }

    public PopularQa answerContent(String answerContent) {
        this.setAnswerContent(answerContent);
        return this;
    }

    public void setAnswerContent(String answerContent) {
        this.answerContent = answerContent;
    }

    public PopularQaCategory getCategory() {
        return this.category;
    }

    public PopularQa category(PopularQaCategory category) {
        this.setCategory(category);
        return this;
    }

    public void setCategory(PopularQaCategory category) {
        this.category = category;
    }

    public Integer getSortOrder() {
        return this.sortOrder;
    }

    public PopularQa sortOrder(Integer sortOrder) {
        this.setSortOrder(sortOrder);
        return this;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Long getClickCount() {
        return this.clickCount;
    }

    public PopularQa clickCount(Long clickCount) {
        this.setClickCount(clickCount);
        return this;
    }

    public void setClickCount(Long clickCount) {
        this.clickCount = clickCount;
    }

    public Boolean getIsEnabled() {
        return this.isEnabled;
    }

    public PopularQa isEnabled(Boolean isEnabled) {
        this.setIsEnabled(isEnabled);
        return this;
    }

    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }

    public Boolean getIsTrending() {
        return this.isTrending;
    }

    public PopularQa isTrending(Boolean isTrending) {
        this.setIsTrending(isTrending);
        return this;
    }

    public void setIsTrending(Boolean isTrending) {
        this.isTrending = isTrending;
    }

    public String getTags() {
        return this.tags;
    }

    public PopularQa tags(String tags) {
        this.setTags(tags);
        return this;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public Integer getVersion() {
        return this.version;
    }

    public PopularQa version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public PopularQa createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public PopularQa createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public PopularQa updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public PopularQa updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public PopularQa isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PopularQa)) {
            return false;
        }
        return getId() != null && getId().equals(((PopularQa) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "PopularQa{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", questionTitle='" + getQuestionTitle() + "'" +
            ", questionContent='" + getQuestionContent() + "'" +
            ", answerContent='" + getAnswerContent() + "'" +
            ", category='" + getCategory() + "'" +
            ", sortOrder=" + getSortOrder() +
            ", clickCount=" + getClickCount() +
            ", isEnabled='" + getIsEnabled() + "'" +
            ", isTrending='" + getIsTrending() + "'" +
            ", tags='" + getTags() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
