package com.whiskerguard.ai.domain;

import static com.whiskerguard.ai.domain.PopularQaTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class PopularQaTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(PopularQa.class);
        PopularQa popularQa1 = getPopularQaSample1();
        PopularQa popularQa2 = new PopularQa();
        assertThat(popularQa1).isNotEqualTo(popularQa2);

        popularQa2.setId(popularQa1.getId());
        assertThat(popularQa1).isEqualTo(popularQa2);

        popularQa2 = getPopularQaSample2();
        assertThat(popularQa1).isNotEqualTo(popularQa2);
    }
}
