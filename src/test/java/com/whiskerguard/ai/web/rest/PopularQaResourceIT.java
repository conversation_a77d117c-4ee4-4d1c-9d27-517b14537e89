package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.PopularQaAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.PopularQa;
import com.whiskerguard.ai.domain.enumeration.PopularQaCategory;
import com.whiskerguard.ai.repository.PopularQaRepository;
import com.whiskerguard.ai.service.dto.PopularQaDTO;
import com.whiskerguard.ai.service.mapper.PopularQaMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link PopularQaResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class PopularQaResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_QUESTION_TITLE = "AAAAAAAAAA";
    private static final String UPDATED_QUESTION_TITLE = "BBBBBBBBBB";

    private static final String DEFAULT_QUESTION_CONTENT = "AAAAAAAAAA";
    private static final String UPDATED_QUESTION_CONTENT = "BBBBBBBBBB";

    private static final String DEFAULT_ANSWER_CONTENT = "AAAAAAAAAA";
    private static final String UPDATED_ANSWER_CONTENT = "BBBBBBBBBB";

    private static final PopularQaCategory DEFAULT_CATEGORY = PopularQaCategory.REGULATORY_COMPLIANCE;
    private static final PopularQaCategory UPDATED_CATEGORY = PopularQaCategory.POLICY_REVIEW;

    private static final Integer DEFAULT_SORT_ORDER = 1;
    private static final Integer UPDATED_SORT_ORDER = 2;

    private static final Long DEFAULT_CLICK_COUNT = 1L;
    private static final Long UPDATED_CLICK_COUNT = 2L;

    private static final Boolean DEFAULT_IS_ENABLED = false;
    private static final Boolean UPDATED_IS_ENABLED = true;

    private static final Boolean DEFAULT_IS_TRENDING = false;
    private static final Boolean UPDATED_IS_TRENDING = true;

    private static final String DEFAULT_TAGS = "AAAAAAAAAA";
    private static final String UPDATED_TAGS = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/popular-qas";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private PopularQaRepository popularQaRepository;

    @Autowired
    private PopularQaMapper popularQaMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restPopularQaMockMvc;

    private PopularQa popularQa;

    private PopularQa insertedPopularQa;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static PopularQa createEntity() {
        return new PopularQa()
            .tenantId(DEFAULT_TENANT_ID)
            .questionTitle(DEFAULT_QUESTION_TITLE)
            .questionContent(DEFAULT_QUESTION_CONTENT)
            .answerContent(DEFAULT_ANSWER_CONTENT)
            .category(DEFAULT_CATEGORY)
            .sortOrder(DEFAULT_SORT_ORDER)
            .clickCount(DEFAULT_CLICK_COUNT)
            .isEnabled(DEFAULT_IS_ENABLED)
            .isTrending(DEFAULT_IS_TRENDING)
            .tags(DEFAULT_TAGS)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static PopularQa createUpdatedEntity() {
        return new PopularQa()
            .tenantId(UPDATED_TENANT_ID)
            .questionTitle(UPDATED_QUESTION_TITLE)
            .questionContent(UPDATED_QUESTION_CONTENT)
            .answerContent(UPDATED_ANSWER_CONTENT)
            .category(UPDATED_CATEGORY)
            .sortOrder(UPDATED_SORT_ORDER)
            .clickCount(UPDATED_CLICK_COUNT)
            .isEnabled(UPDATED_IS_ENABLED)
            .isTrending(UPDATED_IS_TRENDING)
            .tags(UPDATED_TAGS)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        popularQa = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedPopularQa != null) {
            popularQaRepository.delete(insertedPopularQa);
            insertedPopularQa = null;
        }
    }

    @Test
    @Transactional
    void createPopularQa() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the PopularQa
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(popularQa);
        var returnedPopularQaDTO = om.readValue(
            restPopularQaMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            PopularQaDTO.class
        );

        // Validate the PopularQa in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedPopularQa = popularQaMapper.toEntity(returnedPopularQaDTO);
        assertPopularQaUpdatableFieldsEquals(returnedPopularQa, getPersistedPopularQa(returnedPopularQa));

        insertedPopularQa = returnedPopularQa;
    }

    @Test
    @Transactional
    void createPopularQaWithExistingId() throws Exception {
        // Create the PopularQa with an existing ID
        popularQa.setId(1L);
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(popularQa);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restPopularQaMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaDTO)))
            .andExpect(status().isBadRequest());

        // Validate the PopularQa in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        popularQa.setTenantId(null);

        // Create the PopularQa, which fails.
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(popularQa);

        restPopularQaMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkQuestionTitleIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        popularQa.setQuestionTitle(null);

        // Create the PopularQa, which fails.
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(popularQa);

        restPopularQaMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkQuestionContentIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        popularQa.setQuestionContent(null);

        // Create the PopularQa, which fails.
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(popularQa);

        restPopularQaMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkAnswerContentIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        popularQa.setAnswerContent(null);

        // Create the PopularQa, which fails.
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(popularQa);

        restPopularQaMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCategoryIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        popularQa.setCategory(null);

        // Create the PopularQa, which fails.
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(popularQa);

        restPopularQaMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsEnabledIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        popularQa.setIsEnabled(null);

        // Create the PopularQa, which fails.
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(popularQa);

        restPopularQaMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        popularQa.setVersion(null);

        // Create the PopularQa, which fails.
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(popularQa);

        restPopularQaMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        popularQa.setCreatedAt(null);

        // Create the PopularQa, which fails.
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(popularQa);

        restPopularQaMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        popularQa.setUpdatedAt(null);

        // Create the PopularQa, which fails.
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(popularQa);

        restPopularQaMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        popularQa.setIsDeleted(null);

        // Create the PopularQa, which fails.
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(popularQa);

        restPopularQaMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllPopularQas() throws Exception {
        // Initialize the database
        insertedPopularQa = popularQaRepository.saveAndFlush(popularQa);

        // Get all the popularQaList
        restPopularQaMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(popularQa.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].questionTitle").value(hasItem(DEFAULT_QUESTION_TITLE)))
            .andExpect(jsonPath("$.[*].questionContent").value(hasItem(DEFAULT_QUESTION_CONTENT)))
            .andExpect(jsonPath("$.[*].answerContent").value(hasItem(DEFAULT_ANSWER_CONTENT)))
            .andExpect(jsonPath("$.[*].category").value(hasItem(DEFAULT_CATEGORY.toString())))
            .andExpect(jsonPath("$.[*].sortOrder").value(hasItem(DEFAULT_SORT_ORDER)))
            .andExpect(jsonPath("$.[*].clickCount").value(hasItem(DEFAULT_CLICK_COUNT.intValue())))
            .andExpect(jsonPath("$.[*].isEnabled").value(hasItem(DEFAULT_IS_ENABLED)))
            .andExpect(jsonPath("$.[*].isTrending").value(hasItem(DEFAULT_IS_TRENDING)))
            .andExpect(jsonPath("$.[*].tags").value(hasItem(DEFAULT_TAGS)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getPopularQa() throws Exception {
        // Initialize the database
        insertedPopularQa = popularQaRepository.saveAndFlush(popularQa);

        // Get the popularQa
        restPopularQaMockMvc
            .perform(get(ENTITY_API_URL_ID, popularQa.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(popularQa.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.questionTitle").value(DEFAULT_QUESTION_TITLE))
            .andExpect(jsonPath("$.questionContent").value(DEFAULT_QUESTION_CONTENT))
            .andExpect(jsonPath("$.answerContent").value(DEFAULT_ANSWER_CONTENT))
            .andExpect(jsonPath("$.category").value(DEFAULT_CATEGORY.toString()))
            .andExpect(jsonPath("$.sortOrder").value(DEFAULT_SORT_ORDER))
            .andExpect(jsonPath("$.clickCount").value(DEFAULT_CLICK_COUNT.intValue()))
            .andExpect(jsonPath("$.isEnabled").value(DEFAULT_IS_ENABLED))
            .andExpect(jsonPath("$.isTrending").value(DEFAULT_IS_TRENDING))
            .andExpect(jsonPath("$.tags").value(DEFAULT_TAGS))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingPopularQa() throws Exception {
        // Get the popularQa
        restPopularQaMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingPopularQa() throws Exception {
        // Initialize the database
        insertedPopularQa = popularQaRepository.saveAndFlush(popularQa);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the popularQa
        PopularQa updatedPopularQa = popularQaRepository.findById(popularQa.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedPopularQa are not directly saved in db
        em.detach(updatedPopularQa);
        updatedPopularQa
            .tenantId(UPDATED_TENANT_ID)
            .questionTitle(UPDATED_QUESTION_TITLE)
            .questionContent(UPDATED_QUESTION_CONTENT)
            .answerContent(UPDATED_ANSWER_CONTENT)
            .category(UPDATED_CATEGORY)
            .sortOrder(UPDATED_SORT_ORDER)
            .clickCount(UPDATED_CLICK_COUNT)
            .isEnabled(UPDATED_IS_ENABLED)
            .isTrending(UPDATED_IS_TRENDING)
            .tags(UPDATED_TAGS)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(updatedPopularQa);

        restPopularQaMockMvc
            .perform(
                put(ENTITY_API_URL_ID, popularQaDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(popularQaDTO))
            )
            .andExpect(status().isOk());

        // Validate the PopularQa in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedPopularQaToMatchAllProperties(updatedPopularQa);
    }

    @Test
    @Transactional
    void putNonExistingPopularQa() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        popularQa.setId(longCount.incrementAndGet());

        // Create the PopularQa
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(popularQa);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restPopularQaMockMvc
            .perform(
                put(ENTITY_API_URL_ID, popularQaDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(popularQaDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PopularQa in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchPopularQa() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        popularQa.setId(longCount.incrementAndGet());

        // Create the PopularQa
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(popularQa);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPopularQaMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(popularQaDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PopularQa in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamPopularQa() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        popularQa.setId(longCount.incrementAndGet());

        // Create the PopularQa
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(popularQa);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPopularQaMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(popularQaDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the PopularQa in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdatePopularQaWithPatch() throws Exception {
        // Initialize the database
        insertedPopularQa = popularQaRepository.saveAndFlush(popularQa);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the popularQa using partial update
        PopularQa partialUpdatedPopularQa = new PopularQa();
        partialUpdatedPopularQa.setId(popularQa.getId());

        partialUpdatedPopularQa
            .tenantId(UPDATED_TENANT_ID)
            .questionContent(UPDATED_QUESTION_CONTENT)
            .answerContent(UPDATED_ANSWER_CONTENT)
            .sortOrder(UPDATED_SORT_ORDER)
            .isEnabled(UPDATED_IS_ENABLED)
            .isTrending(UPDATED_IS_TRENDING)
            .tags(UPDATED_TAGS)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .updatedBy(UPDATED_UPDATED_BY);

        restPopularQaMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedPopularQa.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedPopularQa))
            )
            .andExpect(status().isOk());

        // Validate the PopularQa in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPopularQaUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedPopularQa, popularQa),
            getPersistedPopularQa(popularQa)
        );
    }

    @Test
    @Transactional
    void fullUpdatePopularQaWithPatch() throws Exception {
        // Initialize the database
        insertedPopularQa = popularQaRepository.saveAndFlush(popularQa);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the popularQa using partial update
        PopularQa partialUpdatedPopularQa = new PopularQa();
        partialUpdatedPopularQa.setId(popularQa.getId());

        partialUpdatedPopularQa
            .tenantId(UPDATED_TENANT_ID)
            .questionTitle(UPDATED_QUESTION_TITLE)
            .questionContent(UPDATED_QUESTION_CONTENT)
            .answerContent(UPDATED_ANSWER_CONTENT)
            .category(UPDATED_CATEGORY)
            .sortOrder(UPDATED_SORT_ORDER)
            .clickCount(UPDATED_CLICK_COUNT)
            .isEnabled(UPDATED_IS_ENABLED)
            .isTrending(UPDATED_IS_TRENDING)
            .tags(UPDATED_TAGS)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restPopularQaMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedPopularQa.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedPopularQa))
            )
            .andExpect(status().isOk());

        // Validate the PopularQa in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPopularQaUpdatableFieldsEquals(partialUpdatedPopularQa, getPersistedPopularQa(partialUpdatedPopularQa));
    }

    @Test
    @Transactional
    void patchNonExistingPopularQa() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        popularQa.setId(longCount.incrementAndGet());

        // Create the PopularQa
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(popularQa);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restPopularQaMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, popularQaDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(popularQaDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PopularQa in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchPopularQa() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        popularQa.setId(longCount.incrementAndGet());

        // Create the PopularQa
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(popularQa);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPopularQaMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(popularQaDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the PopularQa in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamPopularQa() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        popularQa.setId(longCount.incrementAndGet());

        // Create the PopularQa
        PopularQaDTO popularQaDTO = popularQaMapper.toDto(popularQa);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restPopularQaMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(popularQaDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the PopularQa in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deletePopularQa() throws Exception {
        // Initialize the database
        insertedPopularQa = popularQaRepository.saveAndFlush(popularQa);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the popularQa
        restPopularQaMockMvc
            .perform(delete(ENTITY_API_URL_ID, popularQa.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return popularQaRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected PopularQa getPersistedPopularQa(PopularQa popularQa) {
        return popularQaRepository.findById(popularQa.getId()).orElseThrow();
    }

    protected void assertPersistedPopularQaToMatchAllProperties(PopularQa expectedPopularQa) {
        assertPopularQaAllPropertiesEquals(expectedPopularQa, getPersistedPopularQa(expectedPopularQa));
    }

    protected void assertPersistedPopularQaToMatchUpdatableProperties(PopularQa expectedPopularQa) {
        assertPopularQaAllUpdatablePropertiesEquals(expectedPopularQa, getPersistedPopularQa(expectedPopularQa));
    }
}
