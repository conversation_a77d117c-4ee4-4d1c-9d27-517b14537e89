{"annotations": {"changelogDate": "20250624095553"}, "applications": "*", "clientRootFolder": "whiskerguardAiService", "databaseType": "sql", "documentation": "热门问答点击记录实体\\n用于统计和分析用户使用情况", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "员工 ID", "fieldName": "employeeId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "问答 ID", "fieldName": "qaId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "点击时间", "fieldName": "clickTime", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "用户IP", "fieldName": "userIp", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "45"}, {"documentation": "用户代理", "fieldName": "userAgent", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"documentation": "会话ID（关联到后续聊天）", "fieldName": "conversationId", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}], "microserviceName": "whiskerguardAiService", "name": "PopularQaClickLog", "pagination": "pagination", "relationships": [{"otherEntityName": "popularQa", "relationshipName": "qa", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}