package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class PopularQaAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPopularQaAllPropertiesEquals(PopularQa expected, PopularQa actual) {
        assertPopularQaAutoGeneratedPropertiesEquals(expected, actual);
        assertPopularQaAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPopularQaAllUpdatablePropertiesEquals(PopularQa expected, PopularQa actual) {
        assertPopularQaUpdatableFieldsEquals(expected, actual);
        assertPopularQaUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPopularQaAutoGeneratedPropertiesEquals(PopularQa expected, PopularQa actual) {
        assertThat(actual)
            .as("Verify PopularQa auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPopularQaUpdatableFieldsEquals(PopularQa expected, PopularQa actual) {
        assertThat(actual)
            .as("Verify PopularQa relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getQuestionTitle()).as("check questionTitle").isEqualTo(expected.getQuestionTitle()))
            .satisfies(a -> assertThat(a.getQuestionContent()).as("check questionContent").isEqualTo(expected.getQuestionContent()))
            .satisfies(a -> assertThat(a.getAnswerContent()).as("check answerContent").isEqualTo(expected.getAnswerContent()))
            .satisfies(a -> assertThat(a.getCategory()).as("check category").isEqualTo(expected.getCategory()))
            .satisfies(a -> assertThat(a.getSortOrder()).as("check sortOrder").isEqualTo(expected.getSortOrder()))
            .satisfies(a -> assertThat(a.getClickCount()).as("check clickCount").isEqualTo(expected.getClickCount()))
            .satisfies(a -> assertThat(a.getIsEnabled()).as("check isEnabled").isEqualTo(expected.getIsEnabled()))
            .satisfies(a -> assertThat(a.getIsTrending()).as("check isTrending").isEqualTo(expected.getIsTrending()))
            .satisfies(a -> assertThat(a.getTags()).as("check tags").isEqualTo(expected.getTags()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPopularQaUpdatableRelationshipsEquals(PopularQa expected, PopularQa actual) {
        // empty method
    }
}
