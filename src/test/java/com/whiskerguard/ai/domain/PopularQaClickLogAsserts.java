package com.whiskerguard.ai.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class PopularQaClickLogAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPopularQaClickLogAllPropertiesEquals(PopularQaClickLog expected, PopularQaClickLog actual) {
        assertPopularQaClickLogAutoGeneratedPropertiesEquals(expected, actual);
        assertPopularQaClickLogAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPopularQaClickLogAllUpdatablePropertiesEquals(PopularQaClickLog expected, PopularQaClickLog actual) {
        assertPopularQaClickLogUpdatableFieldsEquals(expected, actual);
        assertPopularQaClickLogUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPopularQaClickLogAutoGeneratedPropertiesEquals(PopularQaClickLog expected, PopularQaClickLog actual) {
        assertThat(actual)
            .as("Verify PopularQaClickLog auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPopularQaClickLogUpdatableFieldsEquals(PopularQaClickLog expected, PopularQaClickLog actual) {
        assertThat(actual)
            .as("Verify PopularQaClickLog relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getEmployeeId()).as("check employeeId").isEqualTo(expected.getEmployeeId()))
            .satisfies(a -> assertThat(a.getQa() != null ? a.getQa().getId() : null).as("check qaId").isEqualTo(expected.getQa() != null ? expected.getQa().getId() : null))
            .satisfies(a -> assertThat(a.getClickTime()).as("check clickTime").isEqualTo(expected.getClickTime()))
            .satisfies(a -> assertThat(a.getUserIp()).as("check userIp").isEqualTo(expected.getUserIp()))
            .satisfies(a -> assertThat(a.getUserAgent()).as("check userAgent").isEqualTo(expected.getUserAgent()))
            .satisfies(a -> assertThat(a.getConversationId()).as("check conversationId").isEqualTo(expected.getConversationId()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPopularQaClickLogUpdatableRelationshipsEquals(PopularQaClickLog expected, PopularQaClickLog actual) {
        assertThat(actual)
            .as("Verify PopularQaClickLog relationships")
            .satisfies(a -> assertThat(a.getQa()).as("check qa").isEqualTo(expected.getQa()));
    }
}
