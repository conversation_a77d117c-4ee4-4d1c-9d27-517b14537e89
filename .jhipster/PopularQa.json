{"annotations": {"changelogDate": "20250624095552"}, "applications": "*", "clientRootFolder": "whiskerguardAiService", "databaseType": "sql", "documentation": "AI热门问答实体\\n存储企业管理员维护的常见问题和答案", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "问题标题", "fieldName": "questionTitle", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "200"}, {"documentation": "问题内容", "fieldName": "questionContent", "fieldType": "String", "fieldValidateRules": ["required"]}, {"documentation": "答案内容", "fieldName": "answerContent", "fieldType": "String", "fieldValidateRules": ["required"]}, {"documentation": "问题分类", "fieldName": "category", "fieldType": "PopularQaCategory", "fieldTypeDocumentation": "热门问答分类枚举", "fieldValidateRules": ["required"], "fieldValues": "REGULATORY_COMPLIANCE (外规内化),POLICY_REVIEW (制度审查),CONTRACT_REVIEW (合同审查),GENERAL_CONSULTATION (通用咨询),LEGAL_ADVICE (法律建议),COMMON_QUESTIONS (常见问题)"}, {"documentation": "排序权重（数值越大越靠前）", "fieldName": "sortOrder", "fieldType": "Integer"}, {"documentation": "点击次数", "fieldName": "clickCount", "fieldType": "<PERSON>"}, {"documentation": "是否启用", "fieldName": "isEnabled", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"documentation": "是否为热门推荐", "fieldName": "isTrending", "fieldType": "Boolean"}, {"documentation": "标签（多个标签用逗号分隔）", "fieldName": "tags", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardAiService", "name": "PopularQa", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceImpl"}